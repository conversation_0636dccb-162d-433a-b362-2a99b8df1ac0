namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج العلاقة بين المنتج والمخزن (جدول وسيط)
    /// </summary>
    public class ProductWarehouse
    {
        public int ProductId { get; set; }
        public int WarehouseId { get; set; }
        public decimal Quantity { get; set; }
        
        // العلاقات
        public virtual Product Product { get; set; }
        public virtual Warehouse Warehouse { get; set; }
    }
}
