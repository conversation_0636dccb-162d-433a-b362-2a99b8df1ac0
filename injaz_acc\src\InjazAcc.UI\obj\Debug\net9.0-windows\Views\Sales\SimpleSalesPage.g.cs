﻿#pragma checksum "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "13071AAA997D4DC6627BFEB81A3E8E729C431B58"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InjazAcc.UI.Views.Sales;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InjazAcc.UI.Views.Sales {
    
    
    /// <summary>
    /// SimpleSalesPage
    /// </summary>
    public partial class SimpleSalesPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 128 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgSales;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgReturns;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnNewSale;
        
        #line default
        #line hidden
        
        
        #line 264 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnNewReturn;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSalesReport;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InjazAcc.UI;component/views/sales/simplesalespage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.dgSales = ((System.Windows.Controls.DataGrid)(target));
            
            #line 128 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
            this.dgSales.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.dgSales_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 6:
            this.dgReturns = ((System.Windows.Controls.DataGrid)(target));
            
            #line 186 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
            this.dgReturns.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.dgReturns_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 242 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnShowReport_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.btnNewSale = ((System.Windows.Controls.Button)(target));
            
            #line 257 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
            this.btnNewSale.Click += new System.Windows.RoutedEventHandler(this.btnNewSale_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnNewReturn = ((System.Windows.Controls.Button)(target));
            
            #line 264 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
            this.btnNewReturn.Click += new System.Windows.RoutedEventHandler(this.btnNewReturn_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.btnSalesReport = ((System.Windows.Controls.Button)(target));
            
            #line 271 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
            this.btnSalesReport.Click += new System.Windows.RoutedEventHandler(this.btnSalesReport_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 2:
            
            #line 141 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnViewInvoice_Click);
            
            #line default
            #line hidden
            break;
            case 3:
            
            #line 144 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnEditInvoice_Click);
            
            #line default
            #line hidden
            break;
            case 4:
            
            #line 147 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnPrintInvoice_Click);
            
            #line default
            #line hidden
            break;
            case 5:
            
            #line 150 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnDeleteInvoice_Click);
            
            #line default
            #line hidden
            break;
            case 7:
            
            #line 198 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnViewReturn_Click);
            
            #line default
            #line hidden
            break;
            case 8:
            
            #line 201 "..\..\..\..\..\Views\Sales\SimpleSalesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnPrintReturn_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

