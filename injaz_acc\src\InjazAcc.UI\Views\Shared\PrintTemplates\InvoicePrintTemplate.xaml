<UserControl x:Class="InjazAcc.UI.Views.Shared.PrintTemplates.InvoicePrintTemplate"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:InjazAcc.UI.Views.Shared.PrintTemplates"
             mc:Ignorable="d" 
             d:DesignHeight="1123" d:DesignWidth="794"
             FlowDirection="RightToLeft"
             FontFamily="Arial">
    
    <Grid Background="White" Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- ترويسة الفاتورة -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- معلومات الشركة (يسار) -->
            <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                <TextBlock x:Name="txtCompanyName" Text="شركة نظام إنجاز المحاسبي" FontSize="18" FontWeight="Bold"/>
                <TextBlock x:Name="txtCompanyAddress" Text="الرياض، المملكة العربية السعودية" FontSize="12" Margin="0,5,0,0"/>
                <TextBlock x:Name="txtCompanyPhone" Text="هاتف: 0555555555" FontSize="12" Margin="0,2,0,0"/>
                <TextBlock x:Name="txtCompanyEmail" Text="البريد الإلكتروني: <EMAIL>" FontSize="12" Margin="0,2,0,0"/>
                <TextBlock x:Name="txtCompanyTaxNumber" Text="الرقم الضريبي: *********" FontSize="12" Margin="0,2,0,0"/>
            </StackPanel>
            
            <!-- شعار الشركة (وسط) -->
            <Image Grid.Column="1" x:Name="imgCompanyLogo" Width="100" Height="100" Margin="20,0"/>
            
            <!-- معلومات الفاتورة (يمين) -->
            <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                <TextBlock x:Name="txtInvoiceTitle" Text="فاتورة مبيعات" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right"/>
                <TextBlock x:Name="txtInvoiceNumber" Text="رقم الفاتورة: INV-00001" FontSize="12" Margin="0,10,0,0" HorizontalAlignment="Right"/>
                <TextBlock x:Name="txtInvoiceDate" Text="التاريخ: 01/01/2023" FontSize="12" Margin="0,2,0,0" HorizontalAlignment="Right"/>
                <TextBlock x:Name="txtInvoiceTime" Text="الوقت: 10:00 ص" FontSize="12" Margin="0,2,0,0" HorizontalAlignment="Right"/>
                <TextBlock x:Name="txtPaymentMethod" Text="طريقة الدفع: نقدًا" FontSize="12" Margin="0,2,0,0" HorizontalAlignment="Right"/>
            </StackPanel>
        </Grid>
        
        <!-- معلومات العميل/المورد -->
        <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1" Padding="10" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="معلومات العميل" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock x:Name="txtCustomerName" Text="الاسم: عميل افتراضي" Margin="0,2,0,0"/>
                    <TextBlock x:Name="txtCustomerPhone" Text="الهاتف: 0555555555" Margin="0,2,0,0"/>
                    <TextBlock x:Name="txtCustomerAddress" Text="العنوان: الرياض، المملكة العربية السعودية" Margin="0,2,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1">
                    <TextBlock Text="معلومات إضافية" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock x:Name="txtSalesEmployee" Text="موظف المبيعات: موظف افتراضي" Margin="0,2,0,0"/>
                    <TextBlock x:Name="txtReferenceNumber" Text="الرقم المرجعي: REF-00001" Margin="0,2,0,0"/>
                    <TextBlock x:Name="txtInvoiceStatus" Text="حالة الفاتورة: مدفوعة" Margin="0,2,0,0"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- عنوان جدول المنتجات -->
        <TextBlock Grid.Row="2" Text="تفاصيل الفاتورة" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
        
        <!-- جدول المنتجات -->
        <DataGrid Grid.Row="3" x:Name="dgItems" AutoGenerateColumns="False" IsReadOnly="True" 
                  HeadersVisibility="Column" GridLinesVisibility="All" 
                  BorderBrush="Gray" BorderThickness="1" Background="White"
                  RowBackground="White" AlternatingRowBackground="#F5F5F5">
            <DataGrid.Columns>
                <DataGridTextColumn Header="#" Binding="{Binding Index}" Width="40"/>
                <DataGridTextColumn Header="الكود" Binding="{Binding Code}" Width="80"/>
                <DataGridTextColumn Header="اسم المنتج" Binding="{Binding Name}" Width="*"/>
                <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity, StringFormat=N2}" Width="80"/>
                <DataGridTextColumn Header="السعر" Binding="{Binding Price, StringFormat=N2}" Width="100"/>
                <DataGridTextColumn Header="الخصم" Binding="{Binding Discount, StringFormat=N2}" Width="80"/>
                <DataGridTextColumn Header="الإجمالي" Binding="{Binding Total, StringFormat=N2}" Width="100"/>
            </DataGrid.Columns>
        </DataGrid>
        
        <!-- ملخص الفاتورة -->
        <Grid Grid.Row="4" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>
            
            <!-- ملاحظات -->
            <StackPanel Grid.Column="0" Margin="0,0,20,0">
                <TextBlock Text="ملاحظات" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBlock x:Name="txtNotes" Text="لا توجد ملاحظات" TextWrapping="Wrap"/>
                
                <Border BorderBrush="Gray" BorderThickness="1" Margin="0,20,0,0" Padding="10">
                    <StackPanel>
                        <TextBlock Text="شروط وأحكام" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBlock x:Name="txtTerms" TextWrapping="Wrap">
                            1. يرجى الاحتفاظ بنسخة من الفاتورة للرجوع إليها مستقبلاً.<LineBreak/>
                            2. جميع المبالغ بالريال السعودي ما لم يذكر خلاف ذلك.<LineBreak/>
                            3. لا تقبل المرتجعات بعد 14 يوم من تاريخ الشراء.
                        </TextBlock>
                    </StackPanel>
                </Border>
            </StackPanel>
            
            <!-- المجاميع -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Row="0" Grid.Column="0" Text="إجمالي المنتجات:" HorizontalAlignment="Right" Margin="0,5"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="txtSubtotal" Text="0.00 ر.س" Width="120" HorizontalAlignment="Left" Margin="10,5"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" Text="الخصم:" HorizontalAlignment="Right" Margin="0,5"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="txtTotalDiscount" Text="0.00 ر.س" Width="120" HorizontalAlignment="Left" Margin="10,5"/>
                
                <TextBlock Grid.Row="2" Grid.Column="0" Text="الضريبة (15%):" HorizontalAlignment="Right" Margin="0,5"/>
                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="txtTax" Text="0.00 ر.س" Width="120" HorizontalAlignment="Left" Margin="10,5"/>
                
                <Separator Grid.Row="3" Grid.ColumnSpan="2" Margin="0,5"/>
                
                <TextBlock Grid.Row="4" Grid.Column="0" Text="الإجمالي النهائي:" HorizontalAlignment="Right" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="4" Grid.Column="1" x:Name="txtTotal" Text="0.00 ر.س" Width="120" HorizontalAlignment="Left" FontWeight="Bold" Margin="10,5"/>
                
                <TextBlock Grid.Row="5" Grid.Column="0" Text="المبلغ المسدد:" HorizontalAlignment="Right" Margin="0,5"/>
                <TextBlock Grid.Row="5" Grid.Column="1" x:Name="txtPaidAmount" Text="0.00 ر.س" Width="120" HorizontalAlignment="Left" Margin="10,5"/>
                
                <TextBlock Grid.Row="6" Grid.Column="0" Text="المبلغ المتبقي:" HorizontalAlignment="Right" Margin="0,5"/>
                <TextBlock Grid.Row="6" Grid.Column="1" x:Name="txtRemainingAmount" Text="0.00 ر.س" Width="120" HorizontalAlignment="Left" Margin="10,5"/>
            </Grid>
        </Grid>
        
        <!-- التذييل -->
        <StackPanel Grid.Row="5" Margin="0,20,0,0">
            <Separator/>
            <TextBlock Text="شكراً لتعاملكم معنا" HorizontalAlignment="Center" Margin="0,10,0,0"/>
            <TextBlock x:Name="txtFooter" Text="تم إنشاء هذه الفاتورة بواسطة نظام إنجاز المحاسبي" HorizontalAlignment="Center" Margin="0,5,0,0" FontSize="10"/>
        </StackPanel>
    </Grid>
</UserControl>
