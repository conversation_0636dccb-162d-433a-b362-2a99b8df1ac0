<Window x:Class="InjazAcc.UI.Views.Sales.SaleInvoiceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Sales"
        mc:Ignorable="d"
        Title="فاتورة مبيعات"
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        FontFamily="{materialDesign:MaterialDesignFont}"
        MouseLeftButtonDown="Window_MouseLeftButtonDown">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان الفاتورة -->
        <TextBlock Grid.Row="0" x:Name="txtInvoiceTitle" Text="فاتورة مبيعات جديدة" Style="{StaticResource PageTitle}"/>

        <!-- معلومات الفاتورة -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- معلومات الفاتورة الأساسية -->
            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم الفاتورة:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
                <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtInvoiceNumber" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False" Text="INV-006"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ الفاتورة:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
                <DatePicker Grid.Row="1" Grid.Column="1" x:Name="dpInvoiceDate" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Margin="0,0,0,10"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="المخزن:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
                <ComboBox Grid.Row="2" Grid.Column="1" x:Name="cmbWarehouse" Style="{StaticResource MaterialDesignOutlinedComboBox}" Margin="0,0,0,10">
                    <ComboBoxItem Content="المخزن الرئيسي" IsSelected="True"/>
                    <ComboBoxItem Content="مخزن الفرع الأول"/>
                    <ComboBoxItem Content="مخزن الفرع الثاني"/>
                </ComboBox>
            </Grid>

            <!-- معلومات العميل -->
            <Grid Grid.Column="1" Margin="20,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="العميل:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
                <ComboBox Grid.Row="0" Grid.Column="1" x:Name="cmbCustomer" Style="{StaticResource MaterialDesignOutlinedComboBox}" Margin="0,0,0,10">
                    <ComboBoxItem Content="شركة الأمل التجارية"/>
                    <ComboBoxItem Content="مؤسسة النور"/>
                    <ComboBoxItem Content="شركة الإعمار"/>
                    <ComboBoxItem Content="مؤسسة الفجر"/>
                    <ComboBoxItem Content="شركة البناء الحديث"/>
                </ComboBox>
                <Button Grid.Row="0" Grid.Column="2" Style="{StaticResource MaterialDesignIconButton}" ToolTip="إضافة عميل جديد" Margin="5,0,0,10">
                    <materialDesign:PackIcon Kind="AccountPlusOutline" Width="24" Height="24"/>
                </Button>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="رقم الهاتف:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
                <TextBox Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2" x:Name="txtCustomerPhone" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False" Text="**********"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="الرصيد:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
                <TextBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2" x:Name="txtCustomerBalance" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False" Text="1,750.75 ر.س"/>
            </Grid>
        </Grid>

        <!-- أدوات إدارة الأصناف -->
        <Grid Grid.Row="2" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="أصناف الفاتورة" Style="{StaticResource SectionTitle}"/>

            <Button Grid.Column="1" x:Name="btnAddItem" Style="{StaticResource ActionButton}" Click="btnAddItem_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="CartPlus" Width="20" Height="20" VerticalAlignment="Center"/>
                    <TextBlock Text="إضافة صنف" Margin="8,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </Grid>

        <!-- جدول الأصناف -->
        <DataGrid Grid.Row="3" x:Name="dgItems" Style="{StaticResource DataGridStyle}" AutoGenerateColumns="False" CanUserAddRows="False" FlowDirection="RightToLeft">
            <DataGrid.Columns>
                <DataGridTextColumn Header="الكود" Binding="{Binding Code}" Width="100"/>
                <DataGridTextColumn Header="الصنف" Binding="{Binding Name}" Width="250"/>
                <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                <DataGridTextColumn Header="السعر" Binding="{Binding Price, StringFormat=N2}" Width="100"/>
                <DataGridTextColumn Header="الخصم %" Binding="{Binding DiscountPercentage, StringFormat=N2}" Width="80"/>
                <DataGridTextColumn Header="قيمة الخصم" Binding="{Binding DiscountAmount, StringFormat=N2}" Width="100"/>
                <DataGridTextColumn Header="الضريبة %" Binding="{Binding TaxPercentage, StringFormat=N2}" Width="80"/>
                <DataGridTextColumn Header="قيمة الضريبة" Binding="{Binding TaxAmount, StringFormat=N2}" Width="100"/>
                <DataGridTextColumn Header="الإجمالي" Binding="{Binding Total, StringFormat=N2}" Width="120"/>
                <DataGridTemplateColumn Header="الإجراءات" Width="*">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="تعديل" Click="btnEditItem_Click">
                                    <materialDesign:PackIcon Kind="Pencil" Width="20" Height="20"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="حذف" Click="btnDeleteItem_Click">
                                    <materialDesign:PackIcon Kind="Delete" Width="20" Height="20"/>
                                </Button>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- ملخص الفاتورة -->
        <Grid Grid.Row="4" Margin="0,20,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- ملاحظات الفاتورة -->
            <Grid Grid.Column="0" Margin="0,0,20,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="ملاحظات" Style="{StaticResource FormLabel}"/>
                <TextBox Grid.Row="1" x:Name="txtNotes" Style="{StaticResource MaterialDesignOutlinedTextBox}" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Height="100"/>
            </Grid>

            <!-- إجماليات الفاتورة -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="الإجمالي قبل الضريبة:" Style="{StaticResource FormLabel}" HorizontalAlignment="Left"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="txtSubTotal" Text="0.00 ر.س" Style="{StaticResource FormLabel}" HorizontalAlignment="Right"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="قيمة الخصم:" Style="{StaticResource FormLabel}" HorizontalAlignment="Left"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="txtTotalDiscount" Text="0.00 ر.س" Style="{StaticResource FormLabel}" HorizontalAlignment="Right"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="قيمة الضريبة:" Style="{StaticResource FormLabel}" HorizontalAlignment="Left"/>
                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="txtTotalTax" Text="0.00 ر.س" Style="{StaticResource FormLabel}" HorizontalAlignment="Right"/>

                <Separator Grid.Row="3" Grid.ColumnSpan="2" Margin="0,5"/>

                <TextBlock Grid.Row="4" Grid.Column="0" Text="الإجمالي النهائي:" Style="{StaticResource FormLabel}" FontWeight="Bold" HorizontalAlignment="Left"/>
                <TextBlock Grid.Row="4" Grid.Column="1" x:Name="txtGrandTotal" Text="0.00 ر.س" Style="{StaticResource FormLabel}" FontWeight="Bold" HorizontalAlignment="Right"/>
            </Grid>
        </Grid>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button x:Name="btnSave" Content="حفظ" Style="{StaticResource ActionButton}" Click="btnSave_Click"/>
            <Button x:Name="btnSaveAndPrint" Content="حفظ وطباعة" Style="{StaticResource ActionButton}" Click="btnSaveAndPrint_Click"/>
            <Button x:Name="btnCancel" Content="إلغاء" Style="{StaticResource MaterialDesignOutlinedButton}" Margin="5" Padding="15,5" MinWidth="100" Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
