using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;
using InjazAcc.Core.Exceptions;
using InjazAcc.Services.Helpers;
using InjazAcc.UI.Views.Accounts;
using InjazAcc.UI.Views.Customers;
using InjazAcc.UI.Views.Inventory;
using InjazAcc.UI.Views.Purchases;
using InjazAcc.UI.Views.Reports;
using InjazAcc.UI.Views.Sales;
using InjazAcc.UI.Views.Settings;
using InjazAcc.UI.Views.Suppliers;

namespace InjazAcc.UI.Views.Shared
{
    /// <summary>
    /// Interaction logic for SearchWindow.xaml
    /// </summary>
    public partial class SearchWindow : Window
    {
        private ObservableCollection<SearchResult> _searchResults;
        private List<SearchResult> _allSearchItems;

        public SearchWindow()
        {
            InitializeComponent();

            try
            {
                // تهيئة مجموعات البيانات
                _searchResults = new ObservableCollection<SearchResult>();
                _allSearchItems = new List<SearchResult>();

                // تعيين مصدر البيانات للقائمة
                lvSearchResults.ItemsSource = _searchResults;

                // تحميل بيانات البحث
                LoadSearchData();

                // التركيز على مربع البحث
                txtSearch.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة نافذة البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل بيانات البحث من مختلف أقسام النظام
        /// </summary>
        private void LoadSearchData()
        {
            try
            {
                // إضافة عناصر المبيعات
                _allSearchItems.AddRange(new List<SearchResult>
                {
                    new SearchResult { Title = "فاتورة مبيعات جديدة", Description = "إنشاء فاتورة مبيعات جديدة", Category = "المبيعات", IconKind = PackIconKind.CartPlus, NavigationAction = () => OpenSalesInvoice() },
                    new SearchResult { Title = "مرتجع مبيعات", Description = "إنشاء فاتورة مرتجع مبيعات", Category = "المبيعات", IconKind = PackIconKind.CartRemove, NavigationAction = () => OpenSalesReturn() },
                    new SearchResult { Title = "قائمة فواتير المبيعات", Description = "عرض جميع فواتير المبيعات", Category = "المبيعات", IconKind = PackIconKind.CartOutline, NavigationAction = () => NavigateToSalesPage() }
                });

                // إضافة عناصر المشتريات
                _allSearchItems.AddRange(new List<SearchResult>
                {
                    new SearchResult { Title = "فاتورة مشتريات جديدة", Description = "إنشاء فاتورة مشتريات جديدة", Category = "المشتريات", IconKind = PackIconKind.ShoppingCartPlus, NavigationAction = () => OpenPurchaseInvoice() },
                    new SearchResult { Title = "مرتجع مشتريات", Description = "إنشاء فاتورة مرتجع مشتريات", Category = "المشتريات", IconKind = PackIconKind.ShoppingCartRemove, NavigationAction = () => OpenPurchaseReturn() },
                    new SearchResult { Title = "قائمة فواتير المشتريات", Description = "عرض جميع فواتير المشتريات", Category = "المشتريات", IconKind = PackIconKind.ShoppingOutline, NavigationAction = () => NavigateToPurchasesPage() }
                });

                // إضافة عناصر المخزون
                _allSearchItems.AddRange(new List<SearchResult>
                {
                    new SearchResult { Title = "المخزون", Description = "إدارة المخزون والأصناف", Category = "المخزون", IconKind = PackIconKind.PackageVariantClosed, NavigationAction = () => NavigateToInventoryPage() },
                    new SearchResult { Title = "جرد المخزون", Description = "إجراء جرد للمخزون", Category = "المخزون", IconKind = PackIconKind.ClipboardList, NavigationAction = () => OpenInventoryCount() }
                });

                // إضافة عناصر العملاء
                _allSearchItems.AddRange(new List<SearchResult>
                {
                    new SearchResult { Title = "العملاء", Description = "إدارة بيانات العملاء", Category = "العملاء", IconKind = PackIconKind.AccountMultipleOutline, NavigationAction = () => NavigateToCustomersPage() },
                    new SearchResult { Title = "إضافة عميل جديد", Description = "إضافة عميل جديد إلى النظام", Category = "العملاء", IconKind = PackIconKind.AccountPlusOutline, NavigationAction = () => OpenNewCustomer() }
                });

                // إضافة عناصر الموردين
                _allSearchItems.AddRange(new List<SearchResult>
                {
                    new SearchResult { Title = "الموردين", Description = "إدارة بيانات الموردين", Category = "الموردين", IconKind = PackIconKind.TruckOutline, NavigationAction = () => NavigateToSuppliersPage() },
                    new SearchResult { Title = "إضافة مورد جديد", Description = "إضافة مورد جديد إلى النظام", Category = "الموردين", IconKind = PackIconKind.TruckPlus, NavigationAction = () => OpenNewSupplier() }
                });

                // إضافة عناصر الحسابات
                _allSearchItems.AddRange(new List<SearchResult>
                {
                    new SearchResult { Title = "الحسابات", Description = "إدارة الحسابات المالية", Category = "الحسابات", IconKind = PackIconKind.CashRegister, NavigationAction = () => NavigateToAccountsPage() },
                    new SearchResult { Title = "دليل الحسابات", Description = "عرض وإدارة دليل الحسابات", Category = "الحسابات", IconKind = PackIconKind.BookOpenOutline, NavigationAction = () => NavigateToChartOfAccounts() },
                    new SearchResult { Title = "الخزينة", Description = "إدارة حركات الخزينة", Category = "الحسابات", IconKind = PackIconKind.CashMultiple, NavigationAction = () => NavigateToCashPage() },
                    new SearchResult { Title = "الحسابات الختامية", Description = "عرض التقارير المالية", Category = "الحسابات", IconKind = PackIconKind.ChartLine, NavigationAction = () => NavigateToFinancialStatements() }
                });

                // إضافة عناصر التقارير
                _allSearchItems.AddRange(new List<SearchResult>
                {
                    new SearchResult { Title = "التقارير", Description = "عرض تقارير النظام", Category = "التقارير", IconKind = PackIconKind.ChartLine, NavigationAction = () => NavigateToReportsPage() },
                    new SearchResult { Title = "تقارير المبيعات", Description = "عرض تقارير المبيعات", Category = "التقارير", IconKind = PackIconKind.CartOutline, NavigationAction = () => NavigateToSalesReports() },
                    new SearchResult { Title = "تقارير المشتريات", Description = "عرض تقارير المشتريات", Category = "التقارير", IconKind = PackIconKind.ShoppingOutline, NavigationAction = () => NavigateToPurchasesReports() },
                    new SearchResult { Title = "تقارير المخزون", Description = "عرض تقارير المخزون", Category = "التقارير", IconKind = PackIconKind.PackageVariantClosed, NavigationAction = () => NavigateToInventoryReports() },
                    new SearchResult { Title = "تقارير العملاء والموردين", Description = "عرض تقارير العملاء والموردين", Category = "التقارير", IconKind = PackIconKind.AccountGroupOutline, NavigationAction = () => NavigateToCustomersAndSuppliersReports() }
                });

                // إضافة عناصر الإعدادات
                _allSearchItems.AddRange(new List<SearchResult>
                {
                    new SearchResult { Title = "الإعدادات", Description = "إعدادات النظام", Category = "الإعدادات", IconKind = PackIconKind.Cog, NavigationAction = () => NavigateToSettingsPage() },
                    new SearchResult { Title = "إعدادات الشركة", Description = "تعديل بيانات الشركة", Category = "الإعدادات", IconKind = PackIconKind.Domain, NavigationAction = () => NavigateToCompanySettings() },
                    new SearchResult { Title = "إدارة المستخدمين", Description = "إدارة مستخدمي النظام والصلاحيات", Category = "الإعدادات", IconKind = PackIconKind.AccountCogOutline, NavigationAction = () => NavigateToUserManagement() }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// البحث في البيانات
        /// </summary>
        private void Search()
        {
            try
            {
                string searchText = txtSearch.Text.Trim();

                // مسح النتائج السابقة
                _searchResults.Clear();

                if (string.IsNullOrWhiteSpace(searchText))
                {
                    // إذا كان النص فارغًا، نعرض جميع العناصر
                    foreach (var item in _allSearchItems)
                    {
                        _searchResults.Add(item);
                    }
                }
                else
                {
                    // البحث في العناصر
                    var results = _allSearchItems.Where(item =>
                        item.Title.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                        item.Description.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                        item.Category.Contains(searchText, StringComparison.OrdinalIgnoreCase)
                    ).ToList();

                    // إضافة النتائج إلى القائمة
                    foreach (var item in results)
                    {
                        _searchResults.Add(item);
                    }
                }

                // عرض/إخفاء رسالة عدم وجود نتائج
                txtNoResults.Visibility = _searchResults.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            Search();
        }

        private void txtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                Search();
            }
        }

        private void txtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            // البحث التلقائي عند تغيير النص
            Search();
        }

        private void lvSearchResults_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // الحصول على العنصر المحدد
                var selectedItem = lvSearchResults.SelectedItem as SearchResult;
                if (selectedItem != null && selectedItem.NavigationAction != null)
                {
                    // تنفيذ إجراء التنقل
                    selectedItem.NavigationAction.Invoke();

                    // إغلاق نافذة البحث
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح العنصر المحدد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void Window_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ChangedButton == MouseButton.Left)
                this.DragMove();
        }

        #region Navigation Methods

        // المبيعات
        private void OpenSalesInvoice()
        {
            try
            {
                var window = new NewSaleInvoiceWindow();
                window.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح فاتورة المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenSalesReturn()
        {
            try
            {
                var window = new SaleReturnWindow();
                window.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح مرتجع المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void NavigateToSalesPage()
        {
            NavigateToPage(new SimpleSalesPage());
        }

        // المشتريات
        private void OpenPurchaseInvoice()
        {
            try
            {
                var window = new NewPurchaseInvoiceWindow();
                window.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح فاتورة المشتريات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenPurchaseReturn()
        {
            try
            {
                var window = new PurchaseReturnWindow();
                window.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح مرتجع المشتريات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void NavigateToPurchasesPage()
        {
            NavigateToPage(new SimplePurchasesPage());
        }

        // المخزون
        private void NavigateToInventoryPage()
        {
            NavigateToPage(new InventoryPage());
        }

        private void OpenInventoryCount()
        {
            // تنفيذ فتح نافذة جرد المخزون
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريبًا", "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // العملاء
        private void NavigateToCustomersPage()
        {
            NavigateToPage(new CustomersPage());
        }

        private void OpenNewCustomer()
        {
            try
            {
                var window = new CustomerWindow();
                window.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح نافذة إضافة عميل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // الموردين
        private void NavigateToSuppliersPage()
        {
            NavigateToPage(new SuppliersPage());
        }

        private void OpenNewSupplier()
        {
            try
            {
                var window = new SupplierWindow();
                window.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح نافذة إضافة مورد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // الحسابات
        private void NavigateToAccountsPage()
        {
            NavigateToPage(new AccountsPage());
        }

        private void NavigateToChartOfAccounts()
        {
            NavigateToPage(new ChartOfAccountsPage());
        }

        private void NavigateToCashPage()
        {
            NavigateToPage(new CashPage());
        }

        private void NavigateToFinancialStatements()
        {
            NavigateToPage(new FinancialStatementsPage());
        }

        // التقارير
        private void NavigateToReportsPage()
        {
            NavigateToPage(new ReportsPage());
        }

        private void NavigateToSalesReports()
        {
            NavigateToPage(new SalesReportsPage());
        }

        private void NavigateToPurchasesReports()
        {
            NavigateToPage(new PurchasesReportsPage());
        }

        private void NavigateToInventoryReports()
        {
            NavigateToPage(new InventoryReportsPage());
        }

        private void NavigateToCustomersAndSuppliersReports()
        {
            NavigateToPage(new CustomersAndSuppliersReportsPage());
        }

        // الإعدادات
        private void NavigateToSettingsPage()
        {
            NavigateToPage(new SettingsPage());
        }

        private void NavigateToCompanySettings()
        {
            NavigateToPage(new SettingsPage());
        }

        private void NavigateToUserManagement()
        {
            NavigateToPage(new SettingsPage());
        }

        // طريقة عامة للتنقل إلى صفحة
        private void NavigateToPage(Page page)
        {
            try
            {
                // الحصول على النافذة الرئيسية
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    // تنفيذ التنقل إلى الصفحة المطلوبة
                    mainWindow.NavigateToPage(page);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الانتقال إلى الصفحة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion
    }

    /// <summary>
    /// فئة تمثل نتيجة بحث
    /// </summary>
    public class SearchResult
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public PackIconKind IconKind { get; set; }
        public Action NavigationAction { get; set; }
    }
}
