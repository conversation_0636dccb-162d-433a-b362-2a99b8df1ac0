<Window x:Class="InjazAcc.UI.Views.Accounts.SupplierPaymentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Accounts"
        mc:Ignorable="d"
        Title="دفع لمورد" Height="450" Width="500"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        TextElement.FontSize="14"
        TextElement.FontWeight="Regular"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- عنوان النافذة -->
        <TextBlock Grid.Row="0" Text="دفع لمورد" Style="{StaticResource PageTitle}" HorizontalAlignment="Center" Margin="0,0,0,20"/>
        
        <!-- نموذج إدخال البيانات -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- التاريخ -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="التاريخ:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <DatePicker Grid.Row="0" Grid.Column="1" x:Name="dpDate" Margin="0,5"/>
            
            <!-- المورد -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="المورد:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <ComboBox Grid.Row="1" Grid.Column="1" x:Name="cmbSupplier" Margin="0,5" SelectionChanged="cmbSupplier_SelectionChanged">
                <ComboBoxItem Content="شركة التوريدات العامة"/>
                <ComboBoxItem Content="مؤسسة الإمداد"/>
                <ComboBoxItem Content="شركة المواد الأولية"/>
                <ComboBoxItem Content="مؤسسة التجهيزات"/>
                <ComboBoxItem Content="شركة المعدات المكتبية"/>
            </ComboBox>
            
            <!-- رصيد المورد -->
            <TextBlock Grid.Row="2" Grid.Column="0" Text="رصيد المورد:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtSupplierBalance" IsReadOnly="True" Margin="0,5"/>
            
            <!-- المبلغ -->
            <TextBlock Grid.Row="3" Grid.Column="0" Text="المبلغ:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBox Grid.Row="3" Grid.Column="1" x:Name="txtAmount" Margin="0,5"/>
            
            <!-- طريقة الدفع -->
            <TextBlock Grid.Row="4" Grid.Column="0" Text="طريقة الدفع:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <ComboBox Grid.Row="4" Grid.Column="1" x:Name="cmbPaymentMethod" Margin="0,5">
                <ComboBoxItem Content="نقدي"/>
                <ComboBoxItem Content="شيك"/>
                <ComboBoxItem Content="تحويل بنكي"/>
                <ComboBoxItem Content="بطاقة ائتمان"/>
            </ComboBox>
            
            <!-- البيان -->
            <TextBlock Grid.Row="5" Grid.Column="0" Text="البيان:" Margin="0,0,10,0" VerticalAlignment="Top"/>
            <TextBox Grid.Row="5" Grid.Column="1" x:Name="txtDescription" TextWrapping="Wrap" AcceptsReturn="True" Height="80" Margin="0,5"/>
            
            <!-- المرفقات -->
            <TextBlock Grid.Row="6" Grid.Column="0" Text="المرفقات:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <Grid Grid.Row="6" Grid.Column="1" Margin="0,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBox Grid.Column="0" x:Name="txtAttachment" IsReadOnly="True" Margin="0,0,10,0"/>
                <Button Grid.Column="1" Style="{StaticResource MaterialDesignRaisedButton}" Content="استعراض" Click="btnBrowse_Click"/>
            </Grid>
        </Grid>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="حفظ" Margin="5,0" Click="btnSave_Click"/>
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="إلغاء" Margin="5,0" Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
