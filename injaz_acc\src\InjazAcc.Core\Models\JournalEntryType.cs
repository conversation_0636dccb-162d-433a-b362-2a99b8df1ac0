namespace InjazAcc.Core.Models
{
    /// <summary>
    /// أنواع القيود المحاسبية
    /// </summary>
    public enum JournalEntryType
    {
        /// <summary>
        /// قيد عام
        /// </summary>
        General = 1,
        
        /// <summary>
        /// قيد فاتورة
        /// </summary>
        Invoice = 2,
        
        /// <summary>
        /// قيد دفعة
        /// </summary>
        Payment = 3,
        
        /// <summary>
        /// قيد تسوية مخزون
        /// </summary>
        InventoryAdjustment = 4,
        
        /// <summary>
        /// قيد رصيد افتتاحي
        /// </summary>
        OpeningBalance = 5,
        
        /// <summary>
        /// قيد إقفال
        /// </summary>
        ClosingEntry = 6
    }
}
