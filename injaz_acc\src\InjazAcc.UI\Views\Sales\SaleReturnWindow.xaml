<Window x:Class="InjazAcc.UI.Views.Sales.SaleReturnWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Sales"
        mc:Ignorable="d"
        Title="مردودات المبيعات"
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        FontFamily="{materialDesign:MaterialDesignFont}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان المردود -->
        <TextBlock Grid.Row="0" x:Name="txtReturnTitle" Text="مردودات مبيعات جديدة" Style="{StaticResource PageTitle}"/>

        <!-- معلومات المردود -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- معلومات المردود الأساسية -->
            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="رقم المردود:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
                <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtReturnNumber" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False" Text="RET-003"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ المردود:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
                <DatePicker Grid.Row="1" Grid.Column="1" x:Name="dpReturnDate" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Margin="0,0,0,10"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="رقم الفاتورة:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
                <ComboBox Grid.Row="2" Grid.Column="1" x:Name="cmbInvoiceNumber" Style="{StaticResource MaterialDesignOutlinedComboBox}" Margin="0,0,0,10" SelectionChanged="cmbInvoiceNumber_SelectionChanged">
                    <ComboBoxItem Content="INV-001"/>
                    <ComboBoxItem Content="INV-002"/>
                    <ComboBoxItem Content="INV-003"/>
                    <ComboBoxItem Content="INV-004"/>
                    <ComboBoxItem Content="INV-005"/>
                </ComboBox>

                <TextBlock Grid.Row="3" Grid.Column="0" Text="المخزن:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
                <ComboBox Grid.Row="3" Grid.Column="1" x:Name="cmbWarehouse" Style="{StaticResource MaterialDesignOutlinedComboBox}" Margin="0,0,0,10">
                    <ComboBoxItem Content="المخزن الرئيسي" IsSelected="True"/>
                    <ComboBoxItem Content="مخزن الفرع الأول"/>
                    <ComboBoxItem Content="مخزن الفرع الثاني"/>
                </ComboBox>
            </Grid>

            <!-- معلومات العميل -->
            <Grid Grid.Column="1" Margin="20,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="العميل:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
                <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtCustomer" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="رقم الهاتف:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
                <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtCustomerPhone" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="تاريخ الفاتورة:" Style="{StaticResource FormLabel}" Margin="0,0,10,10"/>
                <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtInvoiceDate" Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,0,10" IsEnabled="False"/>
            </Grid>
        </Grid>

        <!-- أدوات إدارة الأصناف -->
        <Grid Grid.Row="2" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="أصناف المردود" Style="{StaticResource SectionTitle}"/>

            <Button Grid.Column="1" x:Name="btnAddItem" Style="{StaticResource ActionButton}" Click="btnAddItem_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="CartRemove" Width="20" Height="20" VerticalAlignment="Center"/>
                    <TextBlock Text="إضافة صنف" Margin="8,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </Grid>

        <!-- جدول الأصناف -->
        <DataGrid Grid.Row="3" x:Name="dgItems" Style="{StaticResource DataGridStyle}" AutoGenerateColumns="False" CanUserAddRows="False" FlowDirection="RightToLeft">
            <DataGrid.Columns>
                <DataGridTextColumn Header="الكود" Binding="{Binding Code}" Width="100"/>
                <DataGridTextColumn Header="الصنف" Binding="{Binding Name}" Width="250"/>
                <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                <DataGridTextColumn Header="الكمية المباعة" Binding="{Binding SoldQuantity}" Width="120"/>
                <DataGridTextColumn Header="كمية المردود" Binding="{Binding ReturnQuantity}" Width="120"/>
                <DataGridTextColumn Header="السعر" Binding="{Binding Price, StringFormat=N2}" Width="100"/>
                <DataGridTextColumn Header="الإجمالي" Binding="{Binding Total, StringFormat=N2}" Width="120"/>
                <DataGridTemplateColumn Header="الإجراءات" Width="*">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="تعديل" Click="btnEditItem_Click">
                                    <materialDesign:PackIcon Kind="Pencil" Width="20" Height="20"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="حذف" Click="btnDeleteItem_Click">
                                    <materialDesign:PackIcon Kind="Delete" Width="20" Height="20"/>
                                </Button>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- ملخص المردود -->
        <Grid Grid.Row="4" Margin="0,20,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- ملاحظات المردود -->
            <Grid Grid.Column="0" Margin="0,0,20,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="سبب المردود" Style="{StaticResource FormLabel}"/>
                <TextBox Grid.Row="1" x:Name="txtReason" Style="{StaticResource MaterialDesignOutlinedTextBox}" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Height="100"/>
            </Grid>

            <!-- إجماليات المردود -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="إجمالي المردود:" Style="{StaticResource FormLabel}" HorizontalAlignment="Left"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="txtSubTotal" Text="0.00 ر.س" Style="{StaticResource FormLabel}" HorizontalAlignment="Right"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="قيمة الضريبة:" Style="{StaticResource FormLabel}" HorizontalAlignment="Left"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="txtTotalTax" Text="0.00 ر.س" Style="{StaticResource FormLabel}" HorizontalAlignment="Right"/>

                <Separator Grid.Row="2" Grid.ColumnSpan="2" Margin="0,5"/>

                <TextBlock Grid.Row="3" Grid.Column="0" Text="الإجمالي النهائي:" Style="{StaticResource FormLabel}" FontWeight="Bold" HorizontalAlignment="Left"/>
                <TextBlock Grid.Row="3" Grid.Column="1" x:Name="txtGrandTotal" Text="0.00 ر.س" Style="{StaticResource FormLabel}" FontWeight="Bold" HorizontalAlignment="Right"/>
            </Grid>
        </Grid>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button x:Name="btnSave" Content="حفظ" Style="{StaticResource ActionButton}" Click="btnSave_Click"/>
            <Button x:Name="btnSaveAndPrint" Content="حفظ وطباعة" Style="{StaticResource ActionButton}" Click="btnSaveAndPrint_Click"/>
            <Button x:Name="btnCancel" Content="إلغاء" Style="{StaticResource MaterialDesignOutlinedButton}" Margin="5" Padding="15,5" MinWidth="100" Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
