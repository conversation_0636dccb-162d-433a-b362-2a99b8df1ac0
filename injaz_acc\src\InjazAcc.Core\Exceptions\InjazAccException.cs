using System;

namespace InjazAcc.Core.Exceptions
{
    /// <summary>
    /// الفئة الأساسية لجميع الاستثناءات المخصصة في تطبيق إنجاز المحاسبي
    /// </summary>
    public class InjazAccException : Exception
    {
        /// <summary>
        /// رمز الخطأ المرتبط بالاستثناء
        /// </summary>
        public string ErrorCode { get; }

        /// <summary>
        /// مستوى خطورة الاستثناء
        /// </summary>
        public ErrorSeverity Severity { get; }

        /// <summary>
        /// إنشاء استثناء جديد بدون رسالة
        /// </summary>
        public InjazAccException() : base()
        {
            ErrorCode = "ERR-GEN-001";
            Severity = ErrorSeverity.Error;
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        public InjazAccException(string message) : base(message)
        {
            ErrorCode = "ERR-GEN-001";
            Severity = ErrorSeverity.Error;
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        public InjazAccException(string message, string errorCode) : base(message)
        {
            ErrorCode = errorCode;
            Severity = ErrorSeverity.Error;
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ ومستوى خطورة
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="severity">مستوى الخطورة</param>
        public InjazAccException(string message, string errorCode, ErrorSeverity severity) : base(message)
        {
            ErrorCode = errorCode;
            Severity = severity;
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public InjazAccException(string message, Exception innerException) : base(message, innerException)
        {
            ErrorCode = "ERR-GEN-001";
            Severity = ErrorSeverity.Error;
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public InjazAccException(string message, string errorCode, Exception innerException) : base(message, innerException)
        {
            ErrorCode = errorCode;
            Severity = ErrorSeverity.Error;
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ ومستوى خطورة واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="severity">مستوى الخطورة</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public InjazAccException(string message, string errorCode, ErrorSeverity severity, Exception innerException) : base(message, innerException)
        {
            ErrorCode = errorCode;
            Severity = severity;
        }

        /// <summary>
        /// الحصول على رسالة خطأ مفصلة تتضمن رمز الخطأ
        /// </summary>
        /// <returns>رسالة الخطأ المفصلة</returns>
        public virtual string GetDetailedMessage()
        {
            return $"[{ErrorCode}] {Message}";
        }
    }
}
