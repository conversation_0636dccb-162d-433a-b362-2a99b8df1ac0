using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace InjazAcc.UI.Views.Sales
{
    /// <summary>
    /// Interaction logic for SalesPage.xaml
    /// </summary>
    public partial class SalesPage : Page
    {
        public SalesPage()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية لفواتير المبيعات
                var salesList = new List<SaleInvoice>
                {
                    new SaleInvoice { InvoiceNumber = "INV-001", InvoiceDate = DateTime.Now.AddDays(-1), CustomerName = "شركة الأمل التجارية", TotalAmount = 5250.50m, PaidAmount = 5250.50m, RemainingAmount = 0, Status = "مدفوعة" },
                    new SaleInvoice { InvoiceNumber = "INV-002", InvoiceDate = DateTime.Now.AddDays(-3), CustomerName = "مؤسسة النور", TotalAmount = 3750.75m, PaidAmount = 2000.00m, RemainingAmount = 1750.75m, Status = "مدفوعة جزئياً" },
                    new SaleInvoice { InvoiceNumber = "INV-003", InvoiceDate = DateTime.Now.AddDays(-5), CustomerName = "شركة الإعمار", TotalAmount = 8500.00m, PaidAmount = 0, RemainingAmount = 8500.00m, Status = "مؤكدة" },
                    new SaleInvoice { InvoiceNumber = "INV-004", InvoiceDate = DateTime.Now.AddDays(-7), CustomerName = "مؤسسة الفجر", TotalAmount = 4200.25m, PaidAmount = 4200.25m, RemainingAmount = 0, Status = "مدفوعة" },
                    new SaleInvoice { InvoiceNumber = "INV-005", InvoiceDate = DateTime.Now.AddDays(-10), CustomerName = "شركة البناء الحديث", TotalAmount = 3300.00m, PaidAmount = 1500.00m, RemainingAmount = 1800.00m, Status = "مدفوعة جزئياً" }
                };
                dgSales.ItemsSource = salesList;

                // بيانات تجريبية لمردودات المبيعات
                var returnsList = new List<ReturnInvoice>
                {
                    new ReturnInvoice { InvoiceNumber = "RET-001", InvoiceDate = DateTime.Now.AddDays(-2), CustomerName = "شركة الأمل التجارية", OriginalInvoiceNumber = "INV-001", TotalAmount = 750.50m, Status = "مؤكدة" },
                    new ReturnInvoice { InvoiceNumber = "RET-002", InvoiceDate = DateTime.Now.AddDays(-8), CustomerName = "مؤسسة الفجر", OriginalInvoiceNumber = "INV-004", TotalAmount = 800.25m, Status = "مؤكدة" }
                };
                dgReturns.ItemsSource = returnsList;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnNewSale_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة فاتورة مبيعات جديدة
                var invoiceWindow = new SaleInvoiceWindow();
                invoiceWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح نافذة الفاتورة الجديدة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnNewReturn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة مردودات مبيعات جديدة
                var returnWindow = new SaleReturnWindow();
                returnWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح نافذة المردودات الجديدة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void dgSales_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // عرض تفاصيل الفاتورة عند النقر المزدوج
                if (dgSales.SelectedItem != null)
                {
                    var invoice = dgSales.SelectedItem as SaleInvoice;

                    // التحقق من أن الفاتورة ليست فارغة
                    if (invoice == null)
                    {
                        MessageBox.Show("خطأ: الفاتورة المحددة غير موجودة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    var invoiceWindow = new SaleInvoiceWindow(invoice);
                    invoiceWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void dgReturns_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // عرض تفاصيل المردود عند النقر المزدوج
                if (dgReturns.SelectedItem != null)
                {
                    var returnInvoice = dgReturns.SelectedItem as ReturnInvoice;

                    // التحقق من أن المردود ليس فارغاً
                    if (returnInvoice == null)
                    {
                        MessageBox.Show("خطأ: المردود المحدد غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    var returnWindow = new SaleReturnWindow(returnInvoice);
                    returnWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض المردود: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnViewInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // عرض تفاصيل الفاتورة
                var button = sender as Button;
                var invoice = button.DataContext as SaleInvoice;

                // التحقق من أن الفاتورة ليست فارغة
                if (invoice == null)
                {
                    MessageBox.Show("خطأ: الفاتورة المحددة غير موجودة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var invoiceWindow = new SaleInvoiceWindow(invoice, true); // وضع القراءة فقط
                invoiceWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعديل الفاتورة
                var button = sender as Button;
                var invoice = button.DataContext as SaleInvoice;

                // التحقق من أن الفاتورة ليست فارغة
                if (invoice == null)
                {
                    MessageBox.Show("خطأ: الفاتورة المحددة غير موجودة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var invoiceWindow = new SaleInvoiceWindow(invoice);
                invoiceWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تعديل الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // طباعة الفاتورة
                var button = sender as Button;
                var invoice = button.DataContext as SaleInvoice;

                // التحقق من أن الفاتورة ليست فارغة
                if (invoice == null)
                {
                    MessageBox.Show("خطأ: الفاتورة المحددة غير موجودة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                MessageBox.Show($"جاري طباعة الفاتورة رقم {invoice.InvoiceNumber}", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDeleteInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حذف الفاتورة
                var button = sender as Button;
                var invoice = button.DataContext as SaleInvoice;

                // التحقق من أن الفاتورة ليست فارغة
                if (invoice == null)
                {
                    MessageBox.Show("خطأ: الفاتورة المحددة غير موجودة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var result = MessageBox.Show($"هل أنت متأكد من حذف الفاتورة رقم {invoice.InvoiceNumber}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // حذف الفاتورة من قاعدة البيانات
                    MessageBox.Show("تم حذف الفاتورة بنجاح", "تم", MessageBoxButton.OK, MessageBoxImage.Information);

                    // تحديث قائمة الفواتير
                    LoadSampleData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnViewReturn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // عرض تفاصيل المردود
                var button = sender as Button;
                var returnInvoice = button.DataContext as ReturnInvoice;

                // التحقق من أن المردود ليس فارغاً
                if (returnInvoice == null)
                {
                    MessageBox.Show("خطأ: المردود المحدد غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var returnWindow = new SaleReturnWindow(returnInvoice, true); // وضع القراءة فقط
                returnWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض المردود: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintReturn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // طباعة المردود
                var button = sender as Button;
                var returnInvoice = button.DataContext as ReturnInvoice;

                // التحقق من أن المردود ليس فارغاً
                if (returnInvoice == null)
                {
                    MessageBox.Show("خطأ: المردود المحدد غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                MessageBox.Show($"جاري طباعة المردود رقم {returnInvoice.InvoiceNumber}", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة المردود: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    // استخدام فئات البيانات المعرفة في الملفات الأخرى
}
