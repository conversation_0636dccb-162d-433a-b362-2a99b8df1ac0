using System;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Inventory
{
    /// <summary>
    /// Interaction logic for InventoryItemWindow.xaml
    /// </summary>
    public partial class InventoryItemWindow : Window
    {
        private bool _isEditMode = false;
        private InventoryItem _item;

        public InventoryItem Item => _item;

        public InventoryItemWindow()
        {
            InitializeComponent();
            _item = new InventoryItem();
            InitializeControls();
        }

        public InventoryItemWindow(InventoryItem item)
        {
            InitializeComponent();
            _isEditMode = true;
            _item = item;
            InitializeControls();
            LoadItemData();
        }

        private void InitializeControls()
        {
            // تعيين القيم الافتراضية
            if (!_isEditMode)
            {
                txtCode.Text = GenerateNewCode();
                txtAvailableQuantity.Text = "0";
                txtPurchasePrice.Text = "0";
                txtSalePrice.Text = "0";
                txtTotalValue.Text = "0";
                txtMinimumQuantity.Text = "0";
                cmbUnit.SelectedIndex = 0;
            }

            // تغيير عنوان النافذة في حالة التعديل
            if (_isEditMode)
            {
                txtWindowTitle.Text = "تعديل صنف";
                this.Title = "تعديل صنف";
            }
        }

        private string GenerateNewCode()
        {
            // توليد رمز جديد (في التطبيق الحقيقي سيتم جلب الرمز من قاعدة البيانات)
            Random random = new Random();
            return $"P{random.Next(1000, 9999)}";
        }

        private void LoadItemData()
        {
            // تحميل بيانات الصنف في حالة التعديل
            if (_item != null)
            {
                txtCode.Text = _item.Code;
                txtName.Text = _item.Name;
                
                // تحديد الوحدة من القائمة
                foreach (ComboBoxItem item in cmbUnit.Items)
                {
                    if (item.Content.ToString() == _item.Unit)
                    {
                        cmbUnit.SelectedItem = item;
                        break;
                    }
                }
                
                txtAvailableQuantity.Text = _item.AvailableQuantity.ToString();
                txtPurchasePrice.Text = _item.PurchasePrice.ToString();
                txtSalePrice.Text = _item.SalePrice.ToString();
                txtTotalValue.Text = _item.TotalValue.ToString();
                txtMinimumQuantity.Text = "5"; // قيمة افتراضية للحد الأدنى
            }
        }

        private void CalculateTotalValue()
        {
            try
            {
                // حساب القيمة الإجمالية
                if (double.TryParse(txtAvailableQuantity.Text, out double quantity) &&
                    double.TryParse(txtPurchasePrice.Text, out double price))
                {
                    double totalValue = quantity * price;
                    txtTotalValue.Text = totalValue.ToString("N2");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حساب القيمة الإجمالية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void txtAvailableQuantity_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotalValue();
        }

        private void txtPurchasePrice_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotalValue();
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtCode.Text))
                {
                    MessageBox.Show("الرجاء إدخال رمز الصنف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtCode.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم الصنف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtName.Focus();
                    return;
                }

                if (!double.TryParse(txtAvailableQuantity.Text, out double availableQuantity) || availableQuantity < 0)
                {
                    MessageBox.Show("الرجاء إدخال كمية صحيحة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtAvailableQuantity.Focus();
                    return;
                }

                if (!double.TryParse(txtPurchasePrice.Text, out double purchasePrice) || purchasePrice < 0)
                {
                    MessageBox.Show("الرجاء إدخال سعر شراء صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtPurchasePrice.Focus();
                    return;
                }

                if (!double.TryParse(txtSalePrice.Text, out double salePrice) || salePrice < 0)
                {
                    MessageBox.Show("الرجاء إدخال سعر بيع صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtSalePrice.Focus();
                    return;
                }

                if (!double.TryParse(txtTotalValue.Text, out double totalValue))
                {
                    MessageBox.Show("حدث خطأ في حساب القيمة الإجمالية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (!double.TryParse(txtMinimumQuantity.Text, out double minimumQuantity) || minimumQuantity < 0)
                {
                    MessageBox.Show("الرجاء إدخال حد أدنى صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtMinimumQuantity.Focus();
                    return;
                }

                // تحديث بيانات الصنف
                _item.Code = txtCode.Text;
                _item.Name = txtName.Text;
                _item.Unit = (cmbUnit.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "قطعة";
                _item.AvailableQuantity = availableQuantity;
                _item.PurchasePrice = purchasePrice;
                _item.SalePrice = salePrice;
                _item.TotalValue = totalValue;

                // إغلاق النافذة بنجاح
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إلغاء العملية وإغلاق النافذة
            this.DialogResult = false;
        }
    }
}
