﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\InjazAcc.Core\InjazAcc.Core.csproj" />
    <ProjectReference Include="..\InjazAcc.Services\InjazAcc.Services.csproj" />
    <ProjectReference Include="..\InjazAcc.DataAccess\InjazAcc.DataAccess.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\user.png" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <PropertyGroup>
    <EnableDefaultCompileItems>true</EnableDefaultCompileItems>
    <EnableDefaultPageItems>true</EnableDefaultPageItems>
  </PropertyGroup>

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

</Project>
