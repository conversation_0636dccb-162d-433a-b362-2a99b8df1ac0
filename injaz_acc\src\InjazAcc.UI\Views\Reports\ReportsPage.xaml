<Page x:Class="InjazAcc.UI.Views.Reports.ReportsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Reports"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="900"
      Title="التقارير"
      FlowDirection="RightToLeft"
      FontFamily="Arial"
      TextElement.FontSize="14"
      TextElement.FontWeight="Regular"
      TextElement.Foreground="{DynamicResource MaterialDesignBody}"
      Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة وزر الرجوع -->
        <Grid Grid.Row="0" Margin="20,20,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0" Style="{StaticResource MaterialDesignFlatButton}"
                    ToolTip="الرجوع للصفحة الرئيسية" Click="btnBackToMain_Click"
                    HorizontalAlignment="Left" Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ArrowRight" Width="24" Height="24" VerticalAlignment="Center"/>
                    <TextBlock Text="الرجوع للرئيسية" VerticalAlignment="Center" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>

            <TextBlock Grid.Column="1" Text="التقارير" Style="{StaticResource PageTitle}"/>
        </Grid>

        <!-- بطاقات التقارير -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- تقارير المبيعات -->
            <Border Grid.Row="0" Grid.Column="0" Margin="10" Background="#4CAF50" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="SalesReports_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="تقارير المبيعات" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="ChartBar" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="تقارير المبيعات والمرتجعات وتحليل المبيعات" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- تقارير المشتريات -->
            <Border Grid.Row="0" Grid.Column="1" Margin="10" Background="#2196F3" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="PurchasesReports_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="تقارير المشتريات" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="CartOutline" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="تقارير المشتريات والمرتجعات وتحليل المشتريات" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- تقارير المخزون -->
            <Border Grid.Row="1" Grid.Column="0" Margin="10" Background="#FF9800" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="InventoryReports_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="تقارير المخزون" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="Package" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="تقارير المخزون وحركة الأصناف والجرد" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- تقارير العملاء والموردين -->
            <Border Grid.Row="1" Grid.Column="1" Margin="10" Background="#9C27B0" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="CustomersAndSuppliersReports_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="تقارير العملاء والموردين" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="AccountGroup" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="تقارير العملاء والموردين وكشوف الحسابات" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- التقارير المالية -->
            <Border Grid.Row="2" Grid.Column="0" Margin="10" Background="#607D8B" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="FinancialReports_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="التقارير المالية" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="CashRegister" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="التقارير المالية والحسابات الختامية" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- تقارير مخصصة -->
            <Border Grid.Row="2" Grid.Column="1" Margin="10" Background="#E91E63" CornerRadius="10" Cursor="Hand" MouseLeftButtonDown="CustomReports_Click">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <TextBlock Grid.Row="0" Text="تقارير مخصصة" FontSize="18" FontWeight="Bold" Foreground="White" Margin="15,15,15,5" HorizontalAlignment="Center"/>
                    <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <materialDesign:PackIcon Kind="FileChart" Width="64" Height="64" Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="إنشاء تقارير مخصصة حسب احتياجات العمل" Foreground="White" TextWrapping="Wrap" TextAlignment="Center" Margin="15,10"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Page>
