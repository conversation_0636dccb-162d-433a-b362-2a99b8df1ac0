<Window x:Class="InjazAcc.UI.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views"
        mc:Ignorable="d"
        Title="تسجيل الدخول - نظام إنجاز المحاسبي"
        Height="600" Width="1000"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        FontFamily="{materialDesign:MaterialDesignFont}"
        FlowDirection="RightToLeft"
        WindowStyle="None"
        AllowsTransparency="True"
        BorderThickness="0">

    <Window.Resources>
        <Style x:Key="CloseButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="15"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="15"
                                BorderThickness="0">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E81123"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Window.Effect>
        <DropShadowEffect BlurRadius="15" Direction="-90" RenderingBias="Quality" ShadowDepth="2" Color="#DDDDDD"/>
    </Window.Effect>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="400"/>
        </Grid.ColumnDefinitions>

        <!-- الجزء الأيسر - صورة الخلفية -->
        <Grid Grid.Column="0">
            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#009688" Offset="0"/>
                    <GradientStop Color="#00796B" Offset="1"/>
                </LinearGradientBrush>
            </Grid.Background>

            <!-- زر الإغلاق -->
            <Button x:Name="btnClose" Style="{StaticResource CloseButtonStyle}"
                    HorizontalAlignment="Left" VerticalAlignment="Top" Margin="10"
                    Click="btnClose_Click">
                <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
            </Button>

            <!-- محتوى الجزء الأيسر -->
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Margin="40">
                <materialDesign:PackIcon Kind="AccountBalance" Width="100" Height="100"
                                         HorizontalAlignment="Center" Foreground="White"/>
                <TextBlock Text="نظام إنجاز المحاسبي" FontSize="36" FontWeight="Bold"
                           HorizontalAlignment="Center" Margin="0,20,0,0" Foreground="White"/>
                <TextBlock Text="النظام المحاسبي المتكامل لإدارة أعمالك" FontSize="18"
                           HorizontalAlignment="Center" Margin="0,10,0,0" Foreground="White" Opacity="0.9"/>

                <!-- ميزات النظام -->
                <StackPanel Margin="0,40,0,0" HorizontalAlignment="Center">
                    <StackPanel Orientation="Horizontal" Margin="0,8,0,8">
                        <materialDesign:PackIcon Kind="CheckCircle" Width="24" Height="24" Foreground="White" Margin="0,0,10,0"/>
                        <TextBlock Text="إدارة المبيعات والمشتريات بكفاءة عالية" Foreground="White" FontSize="16"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,8,0,8">
                        <materialDesign:PackIcon Kind="CheckCircle" Width="24" Height="24" Foreground="White" Margin="0,0,10,0"/>
                        <TextBlock Text="إدارة المخزون ومراقبة الأصناف" Foreground="White" FontSize="16"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,8,0,8">
                        <materialDesign:PackIcon Kind="CheckCircle" Width="24" Height="24" Foreground="White" Margin="0,0,10,0"/>
                        <TextBlock Text="التقارير المالية والمحاسبية المتكاملة" Foreground="White" FontSize="16"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,8,0,8">
                        <materialDesign:PackIcon Kind="CheckCircle" Width="24" Height="24" Foreground="White" Margin="0,0,10,0"/>
                        <TextBlock Text="إدارة العملاء والموردين بسهولة" Foreground="White" FontSize="16"/>
                    </StackPanel>
                </StackPanel>
            </StackPanel>
        </Grid>

        <!-- الجزء الأيمن - نموذج تسجيل الدخول -->
        <Border Grid.Column="1" Background="White">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- شعار التطبيق والعنوان -->
                <StackPanel Grid.Row="0" VerticalAlignment="Center" HorizontalAlignment="Center" Margin="0,60,0,0">
                    <materialDesign:PackIcon Kind="AccountCircle" Width="80" Height="80"
                                             HorizontalAlignment="Center"
                                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="مرحباً بك" FontSize="28" FontWeight="Bold"
                               HorizontalAlignment="Center" Margin="0,16,0,0"/>
                    <TextBlock Text="قم بتسجيل الدخول للوصول إلى حسابك" FontSize="14"
                               HorizontalAlignment="Center" Margin="0,8,0,0" Foreground="#666666"/>
                </StackPanel>

                <!-- نموذج تسجيل الدخول -->
                <StackPanel Grid.Row="1" Margin="40,20,40,20" VerticalAlignment="Center">
                    <!-- اسم المستخدم -->
                    <TextBox x:Name="txtUsername" Margin="0,8,0,0"
                             materialDesign:HintAssist.Hint="اسم المستخدم"
                             Style="{StaticResource OutlinedTextBoxRTL}"
                             HorizontalContentAlignment="Right"
                             HorizontalAlignment="Stretch"
                             TextAlignment="Right"
                             FontSize="14"
                             Padding="8,8,8,0"
                             BorderThickness="1"
                             materialDesign:TextFieldAssist.HasClearButton="False"
                             materialDesign:TextFieldAssist.RippleOnFocusEnabled="True"
                             materialDesign:TextFieldAssist.UnderlineBrush="{DynamicResource PrimaryHueMidBrush}"
                             KeyDown="txtUsername_KeyDown">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Return" Command="{Binding LoginCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>

                    <!-- كلمة المرور -->
                    <PasswordBox x:Name="txtPassword" Margin="0,20,0,0"
                                 materialDesign:HintAssist.Hint="كلمة المرور"
                                 Style="{StaticResource OutlinedPasswordBoxRTL}"
                                 HorizontalContentAlignment="Right"
                                 HorizontalAlignment="Stretch"
                                 FontSize="14"
                                 Padding="8,8,8,0"
                                 BorderThickness="1"
                                 materialDesign:TextFieldAssist.HasClearButton="False"
                                 materialDesign:TextFieldAssist.RippleOnFocusEnabled="True"
                                 materialDesign:TextFieldAssist.UnderlineBrush="{DynamicResource PrimaryHueMidBrush}"
                                 KeyDown="txtPassword_KeyDown">
                        <PasswordBox.InputBindings>
                            <KeyBinding Key="Return" Command="{Binding LoginCommand}"/>
                        </PasswordBox.InputBindings>
                    </PasswordBox>

                    <!-- تذكرني -->
                    <CheckBox x:Name="chkRememberMe" Content="تذكرني" Margin="0,16,0,0"
                              HorizontalAlignment="Right"/>

                    <!-- رسالة الخطأ -->
                    <TextBlock x:Name="txtError" Foreground="#D32F2F" TextWrapping="Wrap"
                               Margin="0,16,0,0" HorizontalAlignment="Center"
                               Visibility="Collapsed" FontSize="13"/>

                    <!-- زر تسجيل الدخول -->
                    <Button x:Name="btnLogin" Content="تسجيل الدخول" Margin="0,24,0,0" Height="48"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            materialDesign:ButtonAssist.CornerRadius="24"
                            Background="{DynamicResource PrimaryHueMidBrush}"
                            BorderThickness="0"
                            FontSize="16"
                            Click="btnLogin_Click"/>
                </StackPanel>

                <!-- معلومات النظام -->
                <StackPanel Grid.Row="2" Margin="0,0,0,20">
                    <TextBlock Text="© 2025 نظام إنجاز المحاسبي - جميع الحقوق محفوظة"
                               FontSize="12" Foreground="#666666" HorizontalAlignment="Center"
                               Margin="0,16,0,0"/>
                    <TextBlock Text="الإصدار 1.0.0" FontSize="11" Foreground="#999999"
                               HorizontalAlignment="Center" Margin="0,4,0,0"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
