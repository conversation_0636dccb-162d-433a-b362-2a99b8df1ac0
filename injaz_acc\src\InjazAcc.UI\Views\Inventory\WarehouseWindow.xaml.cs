using System;
using System.Windows;

namespace InjazAcc.UI.Views.Inventory
{
    /// <summary>
    /// Interaction logic for WarehouseWindow.xaml
    /// </summary>
    public partial class WarehouseWindow : Window
    {
        private bool _isEditMode = false;
        private Warehouse _warehouse;

        public string WarehouseName => txtName.Text;

        public WarehouseWindow()
        {
            InitializeComponent();
            _warehouse = new Warehouse();
            InitializeControls();
        }

        public WarehouseWindow(Warehouse warehouse)
        {
            InitializeComponent();
            _isEditMode = true;
            _warehouse = warehouse;
            InitializeControls();
            LoadWarehouseData();
        }

        private void InitializeControls()
        {
            // تعيين القيم الافتراضية
            if (!_isEditMode)
            {
                txtCode.Text = GenerateNewCode();
            }

            // تغيير عنوان النافذة في حالة التعديل
            if (_isEditMode)
            {
                txtWindowTitle.Text = "تعديل مخزن";
                this.Title = "تعديل مخزن";
            }
        }

        private string GenerateNewCode()
        {
            // توليد رمز جديد (في التطبيق الحقيقي سيتم جلب الرمز من قاعدة البيانات)
            Random random = new Random();
            return $"W{random.Next(1000, 9999)}";
        }

        private void LoadWarehouseData()
        {
            // تحميل بيانات المخزن في حالة التعديل
            if (_warehouse != null)
            {
                txtCode.Text = _warehouse.Code;
                txtName.Text = _warehouse.Name;
                txtManager.Text = _warehouse.Manager;
                txtAddress.Text = _warehouse.Address;
                txtNotes.Text = _warehouse.Notes;
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtCode.Text))
                {
                    MessageBox.Show("الرجاء إدخال رمز المخزن", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtCode.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم المخزن", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtName.Focus();
                    return;
                }

                // تحديث بيانات المخزن
                _warehouse.Code = txtCode.Text;
                _warehouse.Name = txtName.Text;
                _warehouse.Manager = txtManager.Text;
                _warehouse.Address = txtAddress.Text;
                _warehouse.Notes = txtNotes.Text;

                // إغلاق النافذة بنجاح
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إلغاء العملية وإغلاق النافذة
            this.DialogResult = false;
        }
    }

    public class Warehouse
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Manager { get; set; }
        public string Address { get; set; }
        public string Notes { get; set; }
    }
}
