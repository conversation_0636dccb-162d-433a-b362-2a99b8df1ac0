using System;

namespace InjazAcc.Core.Exceptions
{
    /// <summary>
    /// استثناء مخصص لأخطاء العمليات التجارية
    /// </summary>
    public class BusinessException : InjazAccException
    {
        /// <summary>
        /// إنشاء استثناء جديد بدون رسالة
        /// </summary>
        public BusinessException() : base("حدث خطأ في العملية التجارية", "ERR-BUS-001")
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        public BusinessException(string message) : base(message, "ERR-BUS-001")
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        public BusinessException(string message, string errorCode) : base(message, errorCode)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public BusinessException(string message, Exception innerException) : base(message, "ERR-BUS-001", innerException)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public BusinessException(string message, string errorCode, Exception innerException) : base(message, errorCode, innerException)
        {
        }
    }
}
