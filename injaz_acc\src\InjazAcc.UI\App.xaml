﻿<Application x:Class="InjazAcc.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:InjazAcc.UI"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Teal" SecondaryColor="Amber" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- ضبط تنسيق عام للتطبيق -->
            <Style TargetType="{x:Type FrameworkElement}">
                <Setter Property="FlowDirection" Value="RightToLeft"/>
            </Style>

            <!-- ضبط شريط التمرير ليظهر على اليمين مع تقليل الحجم -->
            <Style TargetType="{x:Type ScrollViewer}">
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
                <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="Margin" Value="0"/>
            </Style>

            <!-- ضبط أحجام حقول الإدخال -->
            <Style TargetType="{x:Type TextBox}" BasedOn="{StaticResource MaterialDesignTextBox}">
                <Setter Property="Padding" Value="8,2"/>
                <Setter Property="Height" Value="32"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="Margin" Value="0,2,0,2"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                <Setter Property="TextAlignment" Value="Right"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
            </Style>

            <!-- تنسيق خاص لحقول الإدخال ذات الحدود -->
            <Style TargetType="{x:Type TextBox}" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}" x:Key="OutlinedTextBoxRTL">
                <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
                <Setter Property="materialDesign:TextFieldAssist.TextBoxViewMargin" Value="1,0,1,0"/>
                <Setter Property="materialDesign:HintAssist.HintOpacity" Value="0.7"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="TextAlignment" Value="Right"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="Height" Value="48"/>
                <Setter Property="Padding" Value="8,8,8,0"/>
                <Setter Property="Margin" Value="0"/>
                <Setter Property="BorderThickness" Value="1"/>
            </Style>

            <!-- تنسيق خاص لحقول كلمة المرور ذات الحدود -->
            <Style TargetType="{x:Type PasswordBox}" BasedOn="{StaticResource MaterialDesignOutlinedPasswordBox}" x:Key="OutlinedPasswordBoxRTL">
                <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
                <Setter Property="materialDesign:TextFieldAssist.TextBoxViewMargin" Value="1,0,1,0"/>
                <Setter Property="materialDesign:HintAssist.HintOpacity" Value="0.7"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="Height" Value="48"/>
                <Setter Property="Padding" Value="8,8,8,0"/>
                <Setter Property="Margin" Value="0"/>
                <Setter Property="BorderThickness" Value="1"/>
            </Style>

            <Style TargetType="{x:Type ComboBox}" BasedOn="{StaticResource MaterialDesignComboBox}">
                <Setter Property="Padding" Value="8,2"/>
                <Setter Property="Height" Value="32"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="Margin" Value="0,2,0,2"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
            </Style>

            <Style TargetType="{x:Type DatePicker}" BasedOn="{StaticResource MaterialDesignDatePicker}">
                <Setter Property="Padding" Value="8,2"/>
                <Setter Property="Height" Value="32"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="Margin" Value="0,2,0,2"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
            </Style>

            <Style TargetType="{x:Type DataGrid}">
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="MinRowHeight" Value="22"/>
                <Setter Property="RowHeight" Value="24"/>
                <Setter Property="Margin" Value="0"/>
                <Setter Property="Padding" Value="0"/>
                <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
                <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
                <Setter Property="HeadersVisibility" Value="Column"/>
            </Style>

            <!-- أنماط مخصصة للتطبيق -->
            <Style x:Key="PageTitle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="20" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="Margin" Value="0,0,0,12" />
                <Setter Property="TextAlignment" Value="Right" />
            </Style>

            <Style x:Key="SectionTitle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="16" />
                <Setter Property="FontWeight" Value="SemiBold" />
                <Setter Property="Margin" Value="0,8,0,8" />
                <Setter Property="TextAlignment" Value="Right" />
            </Style>

            <Style x:Key="FormLabel" TargetType="TextBlock">
                <Setter Property="FontSize" Value="13" />
                <Setter Property="Margin" Value="0,4,0,2" />
                <Setter Property="TextAlignment" Value="Right" />
                <Setter Property="VerticalAlignment" Value="Center" />
            </Style>

            <Style x:Key="ActionButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Margin" Value="3" />
                <Setter Property="Padding" Value="10,2" />
                <Setter Property="MinWidth" Value="80" />
                <Setter Property="Height" Value="28" />
            </Style>

            <Style x:Key="DataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
                <Setter Property="AutoGenerateColumns" Value="False" />
                <Setter Property="CanUserAddRows" Value="False" />
                <Setter Property="CanUserDeleteRows" Value="False" />
                <Setter Property="IsReadOnly" Value="True" />
                <Setter Property="SelectionMode" Value="Single" />
                <Setter Property="SelectionUnit" Value="FullRow" />
                <Setter Property="GridLinesVisibility" Value="All" />
                <Setter Property="BorderThickness" Value="1" />
                <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}" />
                <Setter Property="HorizontalGridLinesBrush" Value="{DynamicResource MaterialDesignDivider}" />
                <Setter Property="VerticalGridLinesBrush" Value="{DynamicResource MaterialDesignDivider}" />
                <Setter Property="RowHeight" Value="24" />
                <Setter Property="MinRowHeight" Value="22" />
                <Setter Property="AlternatingRowBackground" Value="{DynamicResource MaterialDesignBackground}" />
                <Setter Property="FlowDirection" Value="RightToLeft" />
                <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
                <Setter Property="HorizontalScrollBarVisibility" Value="Auto" />
                <Setter Property="ColumnHeaderHeight" Value="30" />
                <Setter Property="HeadersVisibility" Value="Column" />
            </Style>

            <Style TargetType="{x:Type DataGridColumnHeader}">
                <Setter Property="Padding" Value="8,4"/>
                <Setter Property="Background" Value="#EEEEEE"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
            </Style>

            <Style TargetType="{x:Type DataGridCell}">
                <Setter Property="Padding" Value="5,2"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type DataGridCell}">
                            <Border Padding="{TemplateBinding Padding}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    Background="{TemplateBinding Background}">
                                <ContentPresenter VerticalAlignment="Center"/>
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <Style x:Key="StatCard" TargetType="Border">
                <Setter Property="CornerRadius" Value="5" />
                <Setter Property="Padding" Value="10,6" />
                <Setter Property="Margin" Value="3" />
                <Setter Property="Height" Value="80" />
            </Style>

            <Style x:Key="StatTitle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="12" />
                <Setter Property="FontWeight" Value="SemiBold" />
                <Setter Property="Foreground" Value="White" />
                <Setter Property="TextAlignment" Value="Center" />
                <Setter Property="Margin" Value="0,0,0,3" />
            </Style>

            <Style x:Key="StatValue" TargetType="TextBlock">
                <Setter Property="FontSize" Value="16" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="Foreground" Value="White" />
                <Setter Property="TextAlignment" Value="Center" />
            </Style>

            <Style TargetType="{x:Type materialDesign:Card}">
                <Setter Property="Padding" Value="12,10" />
                <Setter Property="Margin" Value="5" />
                <Setter Property="UniformCornerRadius" Value="6" />
            </Style>

            <!-- تنسيق خاص لحقول الإدخال ذات الحدود المستخدمة في البحث والفلترة -->
            <Style TargetType="{x:Type TextBox}" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}" x:Key="MaterialDesignOutlinedTextBox">
                <Setter Property="Height" Value="34"/>
                <Setter Property="Padding" Value="8,2"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                <Setter Property="TextAlignment" Value="Right"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="Margin" Value="0,2,0,2"/>
                <Setter Property="materialDesign:HintAssist.Hint" Value="بحث..."/>
                <Setter Property="materialDesign:HintAssist.IsFloating" Value="False"/>
                <Setter Property="materialDesign:TextFieldAssist.TextBoxViewMargin" Value="1,0,1,0"/>
            </Style>

            <!-- تنسيق خاص لقوائم الاختيار ذات الحدود المستخدمة في البحث والفلترة -->
            <Style TargetType="{x:Type ComboBox}" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}" x:Key="MaterialDesignOutlinedComboBox">
                <Setter Property="Height" Value="34"/>
                <Setter Property="Padding" Value="8,2"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="Margin" Value="0,2,0,2"/>
                <Setter Property="materialDesign:HintAssist.IsFloating" Value="False"/>
            </Style>

            <!-- تنسيق خاص لحقول التاريخ ذات الحدود المستخدمة في البحث والفلترة -->
            <Style TargetType="{x:Type DatePicker}" BasedOn="{StaticResource MaterialDesignOutlinedDatePicker}" x:Key="MaterialDesignOutlinedDatePicker">
                <Setter Property="Height" Value="34"/>
                <Setter Property="Padding" Value="8,2"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
                <Setter Property="FlowDirection" Value="RightToLeft"/>
                <Setter Property="Margin" Value="0,2,0,2"/>
                <Setter Property="materialDesign:HintAssist.IsFloating" Value="False"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
