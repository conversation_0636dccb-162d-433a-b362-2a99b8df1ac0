using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.Win32;
using System.Printing;
using InjazAcc.Core.Exceptions;
using InjazAcc.Core.Models;
using InjazAcc.Services.Helpers;
using InjazAcc.Services.Printing;

namespace InjazAcc.UI.Views.Settings
{
    /// <summary>
    /// Interaction logic for SettingsPage.xaml
    /// </summary>
    public partial class SettingsPage : Page
    {
        // نموذج معلومات الشركة
        private CompanyInfo _companyInfo;

        // مجموعة من إعدادات النظام
        private List<SystemSettings> _systemSettings;

        // مجموعات البيانات للمستخدمين والأدوار والصلاحيات
        private ObservableCollection<User> _users;
        private ObservableCollection<Role> _roles;
        private ObservableCollection<Permission> _permissions;

        // قائمة الطابعات المتاحة
        private List<string> _availablePrinters;

        // نسخ للبحث
        private List<User> _allUsers;
        private List<Role> _allRoles;
        private List<Permission> _allPermissions;

        public SettingsPage()
        {
            InitializeComponent();

            // تهيئة المجموعات
            _users = new ObservableCollection<User>();
            _roles = new ObservableCollection<Role>();
            _permissions = new ObservableCollection<Permission>();

            _allUsers = new List<User>();
            _allRoles = new List<Role>();
            _allPermissions = new List<Permission>();

            // ربط مصادر البيانات
            dgUsers.ItemsSource = _users;
            dgRoles.ItemsSource = _roles;
            dgPermissions.ItemsSource = _permissions;

            // تحميل البيانات
            LoadCompanyInfo();
            LoadSystemSettings();
            LoadPrinters();

            // تحميل بيانات المستخدمين والأدوار والصلاحيات
            try
            {
                LoadUsers();
                LoadRoles();
                LoadPermissions();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات المستخدمين والصلاحيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل معلومات الشركة
        /// </summary>
        private void LoadCompanyInfo()
        {
            try
            {
                // في التطبيق الحقيقي، سيتم جلب البيانات من قاعدة البيانات
                // هنا نستخدم بيانات تجريبية
                _companyInfo = new CompanyInfo
                {
                    Id = 1,
                    Name = "شركة نظام إنجاز المحاسبي",
                    LegalName = "شركة إنجاز للبرمجيات",
                    TaxNumber = "*********",
                    CommercialRegister = "*********",
                    Address = "الرياض، المملكة العربية السعودية",
                    Phone = "**********",
                    Email = "<EMAIL>",
                    Website = "www.injaz-acc.com",
                    LogoPath = "",
                    Currency = "ريال سعودي",
                    CurrencySymbol = "ر.س",
                    EstablishmentDate = new DateTime(2023, 1, 1)
                };

                // عرض البيانات في الحقول
                DisplayCompanyInfo();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل معلومات الشركة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض معلومات الشركة في الحقول
        /// </summary>
        private void DisplayCompanyInfo()
        {
            if (_companyInfo != null)
            {
                txtCompanyName.Text = _companyInfo.Name;
                txtLegalName.Text = _companyInfo.LegalName;
                txtTaxNumber.Text = _companyInfo.TaxNumber;
                txtCommercialRegister.Text = _companyInfo.CommercialRegister;
                txtAddress.Text = _companyInfo.Address;
                txtPhone.Text = _companyInfo.Phone;
                txtEmail.Text = _companyInfo.Email;
                txtWebsite.Text = _companyInfo.Website;
                txtLogoPath.Text = _companyInfo.LogoPath;
                dpEstablishmentDate.SelectedDate = _companyInfo.EstablishmentDate;

                // تحديد العملة من القائمة
                foreach (ComboBoxItem item in cmbCurrency.Items)
                {
                    if (item.Content.ToString().Contains(_companyInfo.Currency))
                    {
                        cmbCurrency.SelectedItem = item;
                        break;
                    }
                }

                txtCurrencySymbol.Text = _companyInfo.CurrencySymbol;
            }
        }

        /// <summary>
        /// تحميل إعدادات النظام
        /// </summary>
        private void LoadSystemSettings()
        {
            try
            {
                // في التطبيق الحقيقي، سيتم جلب البيانات من قاعدة البيانات
                // هنا نستخدم بيانات تجريبية
                _systemSettings = new System.Collections.Generic.List<SystemSettings>
                {
                    new SystemSettings { Id = 1, SettingKey = "AutoBackup", SettingValue = "true", Group = SettingGroup.Backup },
                    new SystemSettings { Id = 2, SettingKey = "ShowWelcomeScreen", SettingValue = "true", Group = SettingGroup.Appearance },
                    new SystemSettings { Id = 3, SettingKey = "AutoUpdate", SettingValue = "true", Group = SettingGroup.General },
                    new SystemSettings { Id = 4, SettingKey = "SendNotifications", SettingValue = "true", Group = SettingGroup.Notification },
                    new SystemSettings { Id = 5, SettingKey = "SalesInvoicePrefix", SettingValue = "INV-", Group = SettingGroup.Invoice },
                    new SystemSettings { Id = 6, SettingKey = "PurchaseInvoicePrefix", SettingValue = "PUR-", Group = SettingGroup.Invoice },
                    new SystemSettings { Id = 7, SettingKey = "DefaultTaxRate", SettingValue = "15", Group = SettingGroup.Invoice },
                    new SystemSettings { Id = 8, SettingKey = "DefaultPaymentPeriod", SettingValue = "30", Group = SettingGroup.Invoice }
                };

                // عرض البيانات في الحقول
                DisplaySystemSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل إعدادات النظام: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض إعدادات النظام في الحقول
        /// </summary>
        private void DisplaySystemSettings()
        {
            if (_systemSettings != null)
            {
                // الإعدادات العامة
                chkAutoBackup.IsChecked = GetBoolSetting("AutoBackup");
                chkShowWelcomeScreen.IsChecked = GetBoolSetting("ShowWelcomeScreen");
                chkAutoUpdate.IsChecked = GetBoolSetting("AutoUpdate");
                chkSendNotifications.IsChecked = GetBoolSetting("SendNotifications");

                // إعدادات الفواتير
                txtSalesInvoicePrefix.Text = GetStringSetting("SalesInvoicePrefix");
                txtPurchaseInvoicePrefix.Text = GetStringSetting("PurchaseInvoicePrefix");
                txtDefaultTaxRate.Text = GetStringSetting("DefaultTaxRate");
                txtDefaultPaymentPeriod.Text = GetStringSetting("DefaultPaymentPeriod");
            }
        }

        /// <summary>
        /// الحصول على قيمة إعداد منطقي
        /// </summary>
        private bool GetBoolSetting(string key)
        {
            var setting = _systemSettings.Find(s => s.SettingKey == key);
            if (setting != null)
            {
                return bool.Parse(setting.SettingValue);
            }
            return false;
        }

        /// <summary>
        /// الحصول على قيمة إعداد نصي
        /// </summary>
        private string GetStringSetting(string key)
        {
            var setting = _systemSettings.Find(s => s.SettingKey == key);
            if (setting != null)
            {
                return setting.SettingValue;
            }
            return string.Empty;
        }

        /// <summary>
        /// حدث النقر على زر استعراض الشعار
        /// </summary>
        private void btnBrowseLogo_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog
                {
                    Filter = "ملفات الصور|*.jpg;*.jpeg;*.png;*.bmp|كل الملفات|*.*",
                    Title = "اختر شعار الشركة"
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    txtLogoPath.Text = openFileDialog.FileName;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر حفظ معلومات الشركة
        /// </summary>
        private void btnSaveCompanyInfo_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtCompanyName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الشركة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtCompanyName.Focus();
                    return;
                }

                // تحديث نموذج معلومات الشركة
                _companyInfo.Name = txtCompanyName.Text;
                _companyInfo.LegalName = txtLegalName.Text;
                _companyInfo.TaxNumber = txtTaxNumber.Text;
                _companyInfo.CommercialRegister = txtCommercialRegister.Text;
                _companyInfo.Address = txtAddress.Text;
                _companyInfo.Phone = txtPhone.Text;
                _companyInfo.Email = txtEmail.Text;
                _companyInfo.Website = txtWebsite.Text;
                _companyInfo.LogoPath = txtLogoPath.Text;
                _companyInfo.EstablishmentDate = dpEstablishmentDate.SelectedDate ?? DateTime.Now;

                // تحديث العملة
                if (cmbCurrency.SelectedItem != null)
                {
                    string selectedCurrency = ((ComboBoxItem)cmbCurrency.SelectedItem).Content.ToString();
                    _companyInfo.Currency = selectedCurrency.Split('(')[0].Trim();
                }

                _companyInfo.CurrencySymbol = txtCurrencySymbol.Text;

                // في التطبيق الحقيقي، سيتم حفظ البيانات في قاعدة البيانات
                // SaveCompanyInfoToDatabase(_companyInfo);

                MessageBox.Show("تم حفظ معلومات الشركة بنجاح", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ معلومات الشركة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر حفظ الإعدادات العامة
        /// </summary>
        private void btnSaveGeneralSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحديث إعدادات النظام العامة
                UpdateSetting("AutoBackup", chkAutoBackup.IsChecked.ToString());
                UpdateSetting("ShowWelcomeScreen", chkShowWelcomeScreen.IsChecked.ToString());
                UpdateSetting("AutoUpdate", chkAutoUpdate.IsChecked.ToString());
                UpdateSetting("SendNotifications", chkSendNotifications.IsChecked.ToString());

                // تحديث إعدادات الفواتير
                UpdateSetting("SalesInvoicePrefix", txtSalesInvoicePrefix.Text);
                UpdateSetting("PurchaseInvoicePrefix", txtPurchaseInvoicePrefix.Text);
                UpdateSetting("DefaultTaxRate", txtDefaultTaxRate.Text);
                UpdateSetting("DefaultPaymentPeriod", txtDefaultPaymentPeriod.Text);

                // تحديث إعدادات الطباعة
                UpdateSetting("InvoicePrinter", cmbInvoicePrinter.SelectedItem?.ToString() ?? "");
                UpdateSetting("ReportPrinter", cmbReportPrinter.SelectedItem?.ToString() ?? "");

                if (cmbInvoicePaperSize.SelectedItem != null)
                {
                    UpdateSetting("InvoicePaperSize", ((ComboBoxItem)cmbInvoicePaperSize.SelectedItem).Content.ToString());
                }

                if (cmbReportPaperSize.SelectedItem != null)
                {
                    UpdateSetting("ReportPaperSize", ((ComboBoxItem)cmbReportPaperSize.SelectedItem).Content.ToString());
                }

                UpdateSetting("PrintLogo", chkPrintLogo.IsChecked.ToString());
                UpdateSetting("PrintPreview", chkPrintPreview.IsChecked.ToString());
                UpdateSetting("AutoPrint", chkAutoPrint.IsChecked.ToString());

                // في التطبيق الحقيقي، سيتم حفظ البيانات في قاعدة البيانات
                // SaveSystemSettingsToDatabase(_systemSettings);

                // تحديث خدمة الطباعة بالإعدادات الجديدة
                PrintService.UpdatePrinterSettings(
                    cmbInvoicePrinter.SelectedItem?.ToString(),
                    cmbReportPrinter.SelectedItem?.ToString(),
                    cmbInvoicePaperSize.SelectedItem != null ? ((ComboBoxItem)cmbInvoicePaperSize.SelectedItem).Content.ToString() : "A4",
                    cmbReportPaperSize.SelectedItem != null ? ((ComboBoxItem)cmbReportPaperSize.SelectedItem).Content.ToString() : "A4",
                    chkPrintLogo.IsChecked ?? true,
                    chkPrintPreview.IsChecked ?? true,
                    chkAutoPrint.IsChecked ?? false
                );

                MessageBox.Show("تم حفظ الإعدادات بنجاح", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث قيمة إعداد
        /// </summary>
        private void UpdateSetting(string key, string value)
        {
            var setting = _systemSettings.Find(s => s.SettingKey == key);
            if (setting != null)
            {
                setting.SettingValue = value;
            }
        }

        /// <summary>
        /// تحميل بيانات المستخدمين
        /// </summary>
        private void LoadUsers()
        {
            try
            {
                // في التطبيق الحقيقي، سيتم جلب البيانات من قاعدة البيانات
                // هنا نستخدم بيانات تجريبية
                _allUsers = new List<User>
                {
                    new User { Id = 1, Username = "admin", FullName = "مدير النظام", Email = "<EMAIL>", PhoneNumber = "**********", IsActive = true, RoleId = 1 },
                    new User { Id = 2, Username = "accountant", FullName = "محاسب النظام", Email = "<EMAIL>", PhoneNumber = "**********", IsActive = true, RoleId = 2 },
                    new User { Id = 3, Username = "sales", FullName = "مسؤول المبيعات", Email = "<EMAIL>", PhoneNumber = "**********", IsActive = true, RoleId = 3 },
                    new User { Id = 4, Username = "inventory", FullName = "مسؤول المخزون", Email = "<EMAIL>", PhoneNumber = "**********", IsActive = true, RoleId = 4 },
                    new User { Id = 5, Username = "user1", FullName = "مستخدم معطل", Email = "<EMAIL>", PhoneNumber = "**********", IsActive = false, RoleId = 5 }
                };

                // نسخ البيانات إلى المجموعة القابلة للمراقبة
                _users.Clear();
                foreach (var user in _allUsers)
                {
                    _users.Add(user);
                }

                // تحديث الإحصائيات
                UpdateUserStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات المستخدمين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل بيانات الأدوار
        /// </summary>
        private void LoadRoles()
        {
            try
            {
                // في التطبيق الحقيقي، سيتم جلب البيانات من قاعدة البيانات
                // هنا نستخدم بيانات تجريبية
                _allRoles = new List<Role>
                {
                    new Role { Id = 1, Name = "مدير النظام", Description = "صلاحيات كاملة للنظام" },
                    new Role { Id = 2, Name = "محاسب", Description = "صلاحيات إدارة الحسابات والتقارير المالية" },
                    new Role { Id = 3, Name = "مسؤول مبيعات", Description = "صلاحيات إدارة المبيعات والعملاء" },
                    new Role { Id = 4, Name = "مسؤول مخزون", Description = "صلاحيات إدارة المخزون والمشتريات" },
                    new Role { Id = 5, Name = "مستخدم عادي", Description = "صلاحيات محدودة للاطلاع فقط" }
                };

                // نسخ البيانات إلى المجموعة القابلة للمراقبة
                _roles.Clear();
                foreach (var role in _allRoles)
                {
                    _roles.Add(role);
                }

                // تحديث الإحصائيات
                UpdateRoleStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات الأدوار: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل بيانات الصلاحيات
        /// </summary>
        private void LoadPermissions()
        {
            try
            {
                // في التطبيق الحقيقي، سيتم جلب البيانات من قاعدة البيانات
                // هنا نستخدم بيانات تجريبية
                _allPermissions = new List<Permission>
                {
                    // صلاحيات المبيعات
                    new Permission { Id = 1, Name = "ViewSales", Description = "عرض المبيعات", Module = "المبيعات" },
                    new Permission { Id = 2, Name = "CreateSales", Description = "إنشاء فاتورة مبيعات", Module = "المبيعات" },
                    new Permission { Id = 3, Name = "EditSales", Description = "تعديل فاتورة مبيعات", Module = "المبيعات" },
                    new Permission { Id = 4, Name = "DeleteSales", Description = "حذف فاتورة مبيعات", Module = "المبيعات" },
                    new Permission { Id = 5, Name = "CreateSalesReturn", Description = "إنشاء مردود مبيعات", Module = "المبيعات" },

                    // صلاحيات المشتريات
                    new Permission { Id = 6, Name = "ViewPurchases", Description = "عرض المشتريات", Module = "المشتريات" },
                    new Permission { Id = 7, Name = "CreatePurchases", Description = "إنشاء فاتورة مشتريات", Module = "المشتريات" },
                    new Permission { Id = 8, Name = "EditPurchases", Description = "تعديل فاتورة مشتريات", Module = "المشتريات" },
                    new Permission { Id = 9, Name = "DeletePurchases", Description = "حذف فاتورة مشتريات", Module = "المشتريات" },
                    new Permission { Id = 10, Name = "CreatePurchasesReturn", Description = "إنشاء مردود مشتريات", Module = "المشتريات" },

                    // صلاحيات المخزون
                    new Permission { Id = 11, Name = "ViewInventory", Description = "عرض المخزون", Module = "المخزون" },
                    new Permission { Id = 12, Name = "CreateInventoryItem", Description = "إضافة صنف جديد", Module = "المخزون" },
                    new Permission { Id = 13, Name = "EditInventoryItem", Description = "تعديل صنف", Module = "المخزون" },
                    new Permission { Id = 14, Name = "DeleteInventoryItem", Description = "حذف صنف", Module = "المخزون" },
                    new Permission { Id = 15, Name = "ManageWarehouses", Description = "إدارة المخازن", Module = "المخزون" },

                    // صلاحيات الحسابات
                    new Permission { Id = 16, Name = "ViewAccounts", Description = "عرض الحسابات", Module = "الحسابات" },
                    new Permission { Id = 17, Name = "ManageAccounts", Description = "إدارة الحسابات", Module = "الحسابات" },
                    new Permission { Id = 18, Name = "ViewFinancialStatements", Description = "عرض القوائم المالية", Module = "الحسابات" },
                    new Permission { Id = 19, Name = "ManageCashTransactions", Description = "إدارة المعاملات النقدية", Module = "الحسابات" },

                    // صلاحيات التقارير
                    new Permission { Id = 20, Name = "ViewReports", Description = "عرض التقارير", Module = "التقارير" },
                    new Permission { Id = 21, Name = "ExportReports", Description = "تصدير التقارير", Module = "التقارير" },
                    new Permission { Id = 22, Name = "PrintReports", Description = "طباعة التقارير", Module = "التقارير" },

                    // صلاحيات الإعدادات
                    new Permission { Id = 23, Name = "ViewSettings", Description = "عرض الإعدادات", Module = "الإعدادات" },
                    new Permission { Id = 24, Name = "ManageCompanySettings", Description = "إدارة إعدادات الشركة", Module = "الإعدادات" },
                    new Permission { Id = 25, Name = "ManageUsers", Description = "إدارة المستخدمين", Module = "الإعدادات" },
                    new Permission { Id = 26, Name = "ManageRoles", Description = "إدارة الأدوار", Module = "الإعدادات" },
                    new Permission { Id = 27, Name = "ManagePermissions", Description = "إدارة الصلاحيات", Module = "الإعدادات" },
                    new Permission { Id = 28, Name = "ManageBackup", Description = "إدارة النسخ الاحتياطي", Module = "الإعدادات" }
                };

                // نسخ البيانات إلى المجموعة القابلة للمراقبة
                _permissions.Clear();
                foreach (var permission in _allPermissions)
                {
                    _permissions.Add(permission);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات الصلاحيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث إحصائيات المستخدمين
        /// </summary>
        private void UpdateUserStatistics()
        {
            try
            {
                int totalUsers = _allUsers.Count;
                int activeUsers = _allUsers.Count(u => u.IsActive);
                int inactiveUsers = totalUsers - activeUsers;

                txtTotalUsers.Text = totalUsers.ToString();
                txtActiveUsers.Text = activeUsers.ToString();
                txtInactiveUsers.Text = inactiveUsers.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث إحصائيات المستخدمين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث إحصائيات الأدوار
        /// </summary>
        private void UpdateRoleStatistics()
        {
            try
            {
                txtTotalRoles.Text = _allRoles.Count.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث إحصائيات الأدوار: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل الطابعات المتاحة في النظام
        /// </summary>
        private void LoadPrinters()
        {
            try
            {
                // الحصول على قائمة الطابعات المتاحة
                _availablePrinters = new List<string>();
                LocalPrintServer printServer = new LocalPrintServer();
                PrintQueueCollection printQueues = printServer.GetPrintQueues();

                foreach (PrintQueue printer in printQueues)
                {
                    _availablePrinters.Add(printer.Name);
                }

                // إضافة الطابعات إلى القوائم المنسدلة
                cmbInvoicePrinter.Items.Clear();
                cmbReportPrinter.Items.Clear();

                foreach (string printer in _availablePrinters)
                {
                    cmbInvoicePrinter.Items.Add(printer);
                    cmbReportPrinter.Items.Add(printer);
                }

                // تحديد الطابعة الافتراضية
                string defaultPrinter = printServer.DefaultPrintQueue?.Name;
                if (!string.IsNullOrEmpty(defaultPrinter))
                {
                    cmbInvoicePrinter.SelectedItem = defaultPrinter;
                    cmbReportPrinter.SelectedItem = defaultPrinter;
                }

                // تحميل إعدادات الطابعات المحفوظة
                string invoicePrinter = GetStringSetting("InvoicePrinter");
                string reportPrinter = GetStringSetting("ReportPrinter");
                string invoicePaperSize = GetStringSetting("InvoicePaperSize");
                string reportPaperSize = GetStringSetting("ReportPaperSize");

                if (!string.IsNullOrEmpty(invoicePrinter) && cmbInvoicePrinter.Items.Contains(invoicePrinter))
                {
                    cmbInvoicePrinter.SelectedItem = invoicePrinter;
                }

                if (!string.IsNullOrEmpty(reportPrinter) && cmbReportPrinter.Items.Contains(reportPrinter))
                {
                    cmbReportPrinter.SelectedItem = reportPrinter;
                }

                // تحديد أحجام الورق المحفوظة
                if (!string.IsNullOrEmpty(invoicePaperSize))
                {
                    foreach (ComboBoxItem item in cmbInvoicePaperSize.Items)
                    {
                        if (item.Content.ToString() == invoicePaperSize)
                        {
                            cmbInvoicePaperSize.SelectedItem = item;
                            break;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(reportPaperSize))
                {
                    foreach (ComboBoxItem item in cmbReportPaperSize.Items)
                    {
                        if (item.Content.ToString() == reportPaperSize)
                        {
                            cmbReportPaperSize.SelectedItem = item;
                            break;
                        }
                    }
                }

                // تحميل إعدادات الطباعة الإضافية
                chkPrintLogo.IsChecked = GetBoolSetting("PrintLogo");
                chkPrintPreview.IsChecked = GetBoolSetting("PrintPreview");
                chkAutoPrint.IsChecked = GetBoolSetting("AutoPrint");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الطابعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر تحديث الطابعات
        /// </summary>
        private void btnRefreshPrinters_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حفظ الطابعات المحددة حاليًا
                string selectedInvoicePrinter = cmbInvoicePrinter.SelectedItem?.ToString();
                string selectedReportPrinter = cmbReportPrinter.SelectedItem?.ToString();

                // إعادة تحميل الطابعات
                LoadPrinters();

                // محاولة استعادة الاختيارات السابقة
                if (!string.IsNullOrEmpty(selectedInvoicePrinter) && cmbInvoicePrinter.Items.Contains(selectedInvoicePrinter))
                {
                    cmbInvoicePrinter.SelectedItem = selectedInvoicePrinter;
                }

                if (!string.IsNullOrEmpty(selectedReportPrinter) && cmbReportPrinter.Items.Contains(selectedReportPrinter))
                {
                    cmbReportPrinter.SelectedItem = selectedReportPrinter;
                }

                MessageBox.Show("تم تحديث قائمة الطابعات بنجاح", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث قائمة الطابعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حدث النقر على زر استعادة الإعدادات الافتراضية
        /// </summary>
        private void btnResetSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBoxResult result = MessageBox.Show(
                    "هل أنت متأكد من رغبتك في استعادة الإعدادات الافتراضية؟",
                    "تأكيد",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إعادة تعيين الإعدادات إلى القيم الافتراضية
                    chkAutoBackup.IsChecked = true;
                    chkShowWelcomeScreen.IsChecked = true;
                    chkAutoUpdate.IsChecked = true;
                    chkSendNotifications.IsChecked = true;

                    txtSalesInvoicePrefix.Text = "INV-";
                    txtPurchaseInvoicePrefix.Text = "PUR-";
                    txtDefaultTaxRate.Text = "15";
                    txtDefaultPaymentPeriod.Text = "30";

                    MessageBox.Show("تم استعادة الإعدادات الافتراضية بنجاح", "تم", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #region دوال المستخدمين

        /// <summary>
        /// البحث في المستخدمين
        /// </summary>
        private void txtSearchUser_KeyUp(object sender, KeyEventArgs e)
        {
            try
            {
                string searchText = txtSearchUser.Text.Trim().ToLower();

                if (string.IsNullOrWhiteSpace(searchText))
                {
                    // إذا كان حقل البحث فارغًا، عرض جميع المستخدمين
                    _users.Clear();
                    foreach (var user in _allUsers)
                    {
                        _users.Add(user);
                    }
                }
                else
                {
                    // البحث في المستخدمين
                    var filteredUsers = _allUsers.Where(u =>
                        u.Username.ToLower().Contains(searchText) ||
                        u.FullName.ToLower().Contains(searchText) ||
                        u.Email.ToLower().Contains(searchText) ||
                        u.PhoneNumber.Contains(searchText)
                    ).ToList();

                    // عرض النتائج
                    _users.Clear();
                    foreach (var user in filteredUsers)
                    {
                        _users.Add(user);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        private void btnAddUser_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // في التطبيق الحقيقي، سيتم فتح نافذة إضافة مستخدم جديد
                MessageBox.Show("سيتم فتح نافذة إضافة مستخدم جديد", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);

                // مثال على إضافة مستخدم جديد
                var newUser = new User
                {
                    Id = _allUsers.Max(u => u.Id) + 1,
                    Username = "newuser",
                    FullName = "مستخدم جديد",
                    Email = "<EMAIL>",
                    PhoneNumber = "0555555560",
                    IsActive = true,
                    RoleId = 5
                };

                // إضافة المستخدم الجديد
                _allUsers.Add(newUser);
                _users.Add(newUser);

                // تحديث الإحصائيات
                UpdateUserStatistics();

                MessageBox.Show("تم إضافة المستخدم بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالجة حدث انتهاء تحرير خلية في جدول المستخدمين
        /// </summary>
        private void dgUsers_CellEditEnding(object sender, System.Windows.Controls.DataGridCellEditEndingEventArgs e)
        {
            try
            {
                // الحصول على المستخدم الذي تم تعديله
                var user = e.Row.Item as User;
                if (user != null)
                {
                    // في التطبيق الحقيقي، سيتم حفظ التغييرات في قاعدة البيانات
                    // UpdateUserInDatabase(user);

                    // تحديث النسخة المحلية
                    var originalUser = _allUsers.FirstOrDefault(u => u.Id == user.Id);
                    if (originalUser != null)
                    {
                        // تحديث البيانات في النسخة الأصلية
                        originalUser.Username = user.Username;
                        originalUser.FullName = user.FullName;
                        originalUser.Email = user.Email;
                        originalUser.PhoneNumber = user.PhoneNumber;
                        originalUser.IsActive = user.IsActive;
                    }

                    // تحديث الإحصائيات
                    UpdateUserStatistics();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ التعديلات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تعديل مستخدم
        /// </summary>
        private void btnEditUser_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على المستخدم المحدد
                var button = sender as Button;
                var user = button.DataContext as User;

                if (user != null)
                {
                    // في التطبيق الحقيقي، سيتم فتح نافذة تعديل المستخدم
                    MessageBox.Show($"يمكنك تعديل بيانات المستخدم {user.FullName} مباشرة في الجدول", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);

                    // تحديد الصف للتعديل
                    dgUsers.SelectedItem = user;
                    dgUsers.CurrentCell = new DataGridCellInfo(user, dgUsers.Columns[1]); // تحديد خلية الاسم الكامل
                    dgUsers.BeginEdit();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تعديل المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        private void btnDeleteUser_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على المستخدم المحدد
                var button = sender as Button;
                var user = button.DataContext as User;

                if (user != null)
                {
                    // تأكيد الحذف
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف المستخدم: {user.FullName}؟",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // حذف المستخدم
                        _allUsers.Remove(user);
                        _users.Remove(user);

                        // تحديث الإحصائيات
                        UpdateUserStatistics();

                        MessageBox.Show("تم حذف المستخدم بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض تفاصيل المستخدم عند النقر المزدوج
        /// </summary>
        private void dgUsers_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (dgUsers.SelectedItem != null)
                {
                    var user = dgUsers.SelectedItem as User;
                    if (user != null)
                    {
                        // في التطبيق الحقيقي، سيتم فتح نافذة تفاصيل المستخدم
                        MessageBox.Show($"تفاصيل المستخدم: {user.FullName}\nاسم المستخدم: {user.Username}\nالبريد الإلكتروني: {user.Email}\nرقم الهاتف: {user.PhoneNumber}\nالحالة: {(user.IsActive ? "نشط" : "غير نشط")}", "تفاصيل المستخدم", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region دوال الأدوار

        /// <summary>
        /// البحث في الأدوار
        /// </summary>
        private void txtSearchRole_KeyUp(object sender, KeyEventArgs e)
        {
            try
            {
                string searchText = txtSearchRole.Text.Trim().ToLower();

                if (string.IsNullOrWhiteSpace(searchText))
                {
                    // إذا كان حقل البحث فارغًا، عرض جميع الأدوار
                    _roles.Clear();
                    foreach (var role in _allRoles)
                    {
                        _roles.Add(role);
                    }
                }
                else
                {
                    // البحث في الأدوار
                    var filteredRoles = _allRoles.Where(r =>
                        r.Name.ToLower().Contains(searchText) ||
                        r.Description.ToLower().Contains(searchText)
                    ).ToList();

                    // عرض النتائج
                    _roles.Clear();
                    foreach (var role in filteredRoles)
                    {
                        _roles.Add(role);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إضافة دور جديد
        /// </summary>
        private void btnAddRole_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // في التطبيق الحقيقي، سيتم فتح نافذة إضافة دور جديد
                MessageBox.Show("سيتم فتح نافذة إضافة دور جديد", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);

                // مثال على إضافة دور جديد
                var newRole = new Role
                {
                    Id = _allRoles.Max(r => r.Id) + 1,
                    Name = "دور جديد",
                    Description = "وصف الدور الجديد"
                };

                // إضافة الدور الجديد
                _allRoles.Add(newRole);
                _roles.Add(newRole);

                // تحديث الإحصائيات
                UpdateRoleStatistics();

                MessageBox.Show("تم إضافة الدور بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة الدور: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تعديل دور
        /// </summary>
        private void btnEditRole_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الدور المحدد
                var button = sender as Button;
                var role = button.DataContext as Role;

                if (role != null)
                {
                    // في التطبيق الحقيقي، سيتم فتح نافذة تعديل الدور
                    MessageBox.Show($"سيتم فتح نافذة تعديل الدور: {role.Name}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);

                    // مثال على تعديل الدور
                    role.Description = role.Description + " (معدل)";

                    // تحديث العرض
                    dgRoles.Items.Refresh();

                    MessageBox.Show("تم تعديل الدور بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تعديل الدور: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إدارة صلاحيات الدور
        /// </summary>
        private void btnManagePermissions_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الدور المحدد
                var button = sender as Button;
                var role = button.DataContext as Role;

                if (role != null)
                {
                    // في التطبيق الحقيقي، سيتم فتح نافذة إدارة صلاحيات الدور
                    MessageBox.Show($"سيتم فتح نافذة إدارة صلاحيات الدور: {role.Name}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إدارة صلاحيات الدور: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حذف دور
        /// </summary>
        private void btnDeleteRole_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الدور المحدد
                var button = sender as Button;
                var role = button.DataContext as Role;

                if (role != null)
                {
                    // تأكيد الحذف
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف الدور: {role.Name}؟",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // التحقق من وجود مستخدمين مرتبطين بهذا الدور
                        var usersWithRole = _allUsers.Where(u => u.RoleId == role.Id).ToList();
                        if (usersWithRole.Any())
                        {
                            MessageBox.Show($"لا يمكن حذف الدور لأنه مرتبط بـ {usersWithRole.Count} مستخدم", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                        }

                        // حذف الدور
                        _allRoles.Remove(role);
                        _roles.Remove(role);

                        // تحديث الإحصائيات
                        UpdateRoleStatistics();

                        MessageBox.Show("تم حذف الدور بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف الدور: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض تفاصيل الدور عند النقر المزدوج
        /// </summary>
        private void dgRoles_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (dgRoles.SelectedItem != null)
                {
                    var role = dgRoles.SelectedItem as Role;
                    if (role != null)
                    {
                        // في التطبيق الحقيقي، سيتم فتح نافذة تفاصيل الدور
                        MessageBox.Show($"تفاصيل الدور: {role.Name}\nالوصف: {role.Description}", "تفاصيل الدور", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region دوال الصلاحيات

        /// <summary>
        /// البحث في الصلاحيات
        /// </summary>
        private void txtSearchPermission_KeyUp(object sender, KeyEventArgs e)
        {
            try
            {
                string searchText = txtSearchPermission.Text.Trim().ToLower();

                if (string.IsNullOrWhiteSpace(searchText))
                {
                    // إذا كان حقل البحث فارغًا، عرض جميع الصلاحيات
                    _permissions.Clear();
                    foreach (var permission in _allPermissions)
                    {
                        _permissions.Add(permission);
                    }
                }
                else
                {
                    // البحث في الصلاحيات
                    var filteredPermissions = _allPermissions.Where(p =>
                        p.Name.ToLower().Contains(searchText) ||
                        p.Description.ToLower().Contains(searchText) ||
                        p.Module.ToLower().Contains(searchText)
                    ).ToList();

                    // عرض النتائج
                    _permissions.Clear();
                    foreach (var permission in filteredPermissions)
                    {
                        _permissions.Add(permission);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion
    }
}
