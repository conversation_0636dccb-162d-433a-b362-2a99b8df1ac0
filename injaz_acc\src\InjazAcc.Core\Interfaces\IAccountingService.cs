using System;
using System.Threading.Tasks;
using InjazAcc.Core.Models;

namespace InjazAcc.Core.Interfaces
{
    /// <summary>
    /// واجهة خدمة القيود المحاسبية
    /// </summary>
    public interface IAccountingService
    {
        /// <summary>
        /// إنشاء قيد محاسبي لفاتورة مبيعات
        /// </summary>
        /// <param name="invoice">الفاتورة</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>القيد المحاسبي</returns>
        Task<JournalEntry> CreateSalesInvoiceEntryAsync(Invoice invoice, int userId);
        
        /// <summary>
        /// إنشاء قيد محاسبي لفاتورة مشتريات
        /// </summary>
        /// <param name="invoice">الفاتورة</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>القيد المحاسبي</returns>
        Task<JournalEntry> CreatePurchaseInvoiceEntryAsync(Invoice invoice, int userId);
        
        /// <summary>
        /// إنشاء قيد محاسبي لمردودات مبيعات
        /// </summary>
        /// <param name="invoice">الفاتورة</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>القيد المحاسبي</returns>
        Task<JournalEntry> CreateSalesReturnEntryAsync(Invoice invoice, int userId);
        
        /// <summary>
        /// إنشاء قيد محاسبي لمردودات مشتريات
        /// </summary>
        /// <param name="invoice">الفاتورة</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>القيد المحاسبي</returns>
        Task<JournalEntry> CreatePurchaseReturnEntryAsync(Invoice invoice, int userId);
        
        /// <summary>
        /// إنشاء قيد محاسبي لدفعة من عميل
        /// </summary>
        /// <param name="payment">الدفعة</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>القيد المحاسبي</returns>
        Task<JournalEntry> CreateCustomerPaymentEntryAsync(Payment payment, int userId);
        
        /// <summary>
        /// إنشاء قيد محاسبي لدفعة لمورد
        /// </summary>
        /// <param name="payment">الدفعة</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>القيد المحاسبي</returns>
        Task<JournalEntry> CreateSupplierPaymentEntryAsync(Payment payment, int userId);
        
        /// <summary>
        /// إنشاء قيد محاسبي لمصروف
        /// </summary>
        /// <param name="payment">الدفعة</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>القيد المحاسبي</returns>
        Task<JournalEntry> CreateExpenseEntryAsync(Payment payment, int userId);
        
        /// <summary>
        /// إنشاء قيد محاسبي لإيراد
        /// </summary>
        /// <param name="payment">الدفعة</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>القيد المحاسبي</returns>
        Task<JournalEntry> CreateRevenueEntryAsync(Payment payment, int userId);
        
        /// <summary>
        /// إنشاء قيد محاسبي لتسوية مخزون
        /// </summary>
        /// <param name="adjustment">تسوية المخزون</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>القيد المحاسبي</returns>
        Task<JournalEntry> CreateInventoryAdjustmentEntryAsync(InventoryTransfer adjustment, int userId);
        
        /// <summary>
        /// الحصول على معرف الحساب حسب النوع
        /// </summary>
        /// <param name="accountType">نوع الحساب</param>
        /// <returns>معرف الحساب</returns>
        Task<int> GetAccountIdByTypeAsync(AccountType accountType);
        
        /// <summary>
        /// الحصول على معرف حساب الخزينة
        /// </summary>
        /// <returns>معرف حساب الخزينة</returns>
        Task<int> GetTreasuryAccountIdAsync();
        
        /// <summary>
        /// الحصول على معرف حساب المخزون
        /// </summary>
        /// <returns>معرف حساب المخزون</returns>
        Task<int> GetInventoryAccountIdAsync();
        
        /// <summary>
        /// الحصول على معرف حساب المبيعات
        /// </summary>
        /// <returns>معرف حساب المبيعات</returns>
        Task<int> GetSalesAccountIdAsync();
        
        /// <summary>
        /// الحصول على معرف حساب تكلفة المبيعات
        /// </summary>
        /// <returns>معرف حساب تكلفة المبيعات</returns>
        Task<int> GetCostOfSalesAccountIdAsync();
        
        /// <summary>
        /// الحصول على معرف حساب العملاء
        /// </summary>
        /// <returns>معرف حساب العملاء</returns>
        Task<int> GetCustomersAccountIdAsync();
        
        /// <summary>
        /// الحصول على معرف حساب الموردين
        /// </summary>
        /// <returns>معرف حساب الموردين</returns>
        Task<int> GetSuppliersAccountIdAsync();
    }
}
