<Window x:Class="InjazAcc.UI.Views.Inventory.InventoryItemDetailsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Inventory"
        mc:Ignorable="d"
        Title="تفاصيل الصنف" Height="550" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        TextElement.FontSize="14"
        TextElement.FontWeight="Regular"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- عنوان النافذة -->
        <TextBlock Grid.Row="0" x:Name="txtItemName" Text="تفاصيل الصنف: لابتوب HP ProBook" Style="{StaticResource PageTitle}" HorizontalAlignment="Center" Margin="0,0,0,20"/>
        
        <!-- معلومات الصنف -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- معلومات الصنف - الجانب الأيمن -->
            <Grid Grid.Column="0" Margin="0,0,10,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Row="0" Grid.Column="0" Text="رمز الصنف:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="txtCode" Text="P001" Margin="10,5"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" Text="اسم الصنف:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="txtName" Text="لابتوب HP ProBook" Margin="10,5"/>
                
                <TextBlock Grid.Row="2" Grid.Column="0" Text="الوحدة:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="txtUnit" Text="قطعة" Margin="10,5"/>
                
                <TextBlock Grid.Row="3" Grid.Column="0" Text="الكمية المتاحة:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="3" Grid.Column="1" x:Name="txtAvailableQuantity" Text="15" Margin="10,5"/>
            </Grid>
            
            <!-- معلومات الصنف - الجانب الأيسر -->
            <Grid Grid.Column="1" Margin="10,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Row="0" Grid.Column="0" Text="سعر الشراء:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="txtPurchasePrice" Text="3,000.00 ر.س" Margin="10,5"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" Text="سعر البيع:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="txtSalePrice" Text="3,500.00 ر.س" Margin="10,5"/>
                
                <TextBlock Grid.Row="2" Grid.Column="0" Text="القيمة الإجمالية:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="txtTotalValue" Text="45,000.00 ر.س" Margin="10,5"/>
                
                <TextBlock Grid.Row="3" Grid.Column="0" Text="الحد الأدنى:" FontWeight="Bold" Margin="0,5"/>
                <TextBlock Grid.Row="3" Grid.Column="1" x:Name="txtMinimumQuantity" Text="5" Margin="10,5"/>
            </Grid>
        </Grid>
        
        <!-- تبويبات التفاصيل -->
        <TabControl Grid.Row="2" Style="{StaticResource MaterialDesignTabControl}">
            <!-- تبويب حركة الصنف -->
            <TabItem Header="حركة الصنف">
                <DataGrid x:Name="dgItemMovement" AutoGenerateColumns="False" CanUserAddRows="False" Style="{StaticResource DataGridStyle}">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=yyyy-MM-dd}" Width="120"/>
                        <DataGridTextColumn Header="رقم المستند" Binding="{Binding DocumentNumber}" Width="120"/>
                        <DataGridTextColumn Header="نوع الحركة" Binding="{Binding MovementType}" Width="120"/>
                        <DataGridTextColumn Header="الكمية الواردة" Binding="{Binding InQuantity, StringFormat=N2}" Width="120"/>
                        <DataGridTextColumn Header="الكمية الصادرة" Binding="{Binding OutQuantity, StringFormat=N2}" Width="120"/>
                        <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance, StringFormat=N2}" Width="120"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>
            
            <!-- تبويب المخازن -->
            <TabItem Header="توزيع المخازن">
                <DataGrid x:Name="dgItemWarehouses" AutoGenerateColumns="False" CanUserAddRows="False" Style="{StaticResource DataGridStyle}">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="المخزن" Binding="{Binding WarehouseName}" Width="*"/>
                        <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity, StringFormat=N2}" Width="120"/>
                        <DataGridTextColumn Header="القيمة" Binding="{Binding Value, StringFormat=N2}" Width="120"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>
            
            <!-- تبويب الباركود -->
            <TabItem Header="الباركود">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20">
                        <Button Style="{StaticResource ActionButton}" Content="طباعة الباركود" Click="btnPrintBarcode_Click"/>
                    </StackPanel>
                    
                    <Image Grid.Row="1" Source="/InjazAcc.UI;component/Resources/barcode_sample.png" Width="300" Height="150"/>
                </Grid>
            </TabItem>
        </TabControl>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Style="{StaticResource ActionButton}" Content="طباعة التقرير" Margin="5,0" Click="btnPrintReport_Click"/>
            <Button Style="{StaticResource ActionButton}" Content="إغلاق" Margin="5,0" Click="btnClose_Click"/>
        </StackPanel>
    </Grid>
</Window>
