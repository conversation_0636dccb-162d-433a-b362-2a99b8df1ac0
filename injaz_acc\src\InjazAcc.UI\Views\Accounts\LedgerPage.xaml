<Page x:Class="InjazAcc.UI.Views.Accounts.LedgerPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Accounts"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="900"
      Title="حساب الأستاذ"
      FlowDirection="RightToLeft"
      FontFamily="Arial"
      TextElement.FontSize="14"
      TextElement.FontWeight="Regular"
      TextElement.Foreground="{DynamicResource MaterialDesignBody}"
      Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة وزر الرجوع -->
        <Grid Grid.Row="0" Margin="20,20,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0" Style="{StaticResource MaterialDesignFlatButton}"
                    ToolTip="الرجوع للحسابات" Click="btnBackToAccounts_Click"
                    HorizontalAlignment="Left" Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ArrowRight" Width="24" Height="24" VerticalAlignment="Center"/>
                    <TextBlock Text="الرجوع للحسابات" VerticalAlignment="Center" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>

            <TextBlock Grid.Column="1" Text="حساب الأستاذ" Style="{StaticResource PageTitle}"/>
        </Grid>

        <!-- اختيار الحساب والفترة -->
        <Grid Grid.Row="1" Margin="20,0,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- اختيار الحساب -->
            <TextBlock Grid.Column="0" Text="الحساب:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <ComboBox Grid.Column="1" x:Name="cmbAccount" Margin="0,0,20,0" SelectionChanged="cmbAccount_SelectionChanged">
                <ComboBoxItem Content="1010 - الصندوق"/>
                <ComboBoxItem Content="1020 - البنك"/>
                <ComboBoxItem Content="1030 - المخزون"/>
                <ComboBoxItem Content="1040 - العملاء"/>
                <ComboBoxItem Content="1050 - أثاث ومعدات"/>
                <ComboBoxItem Content="2010 - الموردين"/>
                <ComboBoxItem Content="2020 - قروض قصيرة الأجل"/>
                <ComboBoxItem Content="2030 - مصروفات مستحقة"/>
                <ComboBoxItem Content="3010 - رأس المال"/>
                <ComboBoxItem Content="3020 - الأرباح المحتجزة"/>
                <ComboBoxItem Content="4010 - المبيعات"/>
                <ComboBoxItem Content="4020 - إيرادات أخرى"/>
                <ComboBoxItem Content="5010 - تكلفة المبيعات"/>
                <ComboBoxItem Content="5020 - رواتب وأجور"/>
                <ComboBoxItem Content="5030 - إيجارات"/>
                <ComboBoxItem Content="5040 - مصروفات عمومية وإدارية"/>
            </ComboBox>

            <!-- اختيار الفترة -->
            <TextBlock Grid.Column="2" Text="من:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <DatePicker Grid.Column="3" x:Name="dpFromDate" Width="120" Margin="0,0,20,0"/>

            <TextBlock Grid.Column="4" Text="إلى:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <DatePicker Grid.Column="5" x:Name="dpToDate" Width="120"/>
        </Grid>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="عرض" Margin="5,0" Click="btnShow_Click"/>
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="طباعة" Margin="5,0" Click="btnPrint_Click"/>
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="تصدير Excel" Margin="5,0" Click="btnExport_Click"/>
        </StackPanel>

        <!-- معلومات الحساب -->
        <Border Grid.Row="3" BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="1" Margin="20,0,20,10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- معلومات الحساب -->
                <Grid Grid.Row="0" Background="{DynamicResource MaterialDesignBackground}" Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <TextBlock Text="رقم الحساب:" FontWeight="Bold" Margin="0,0,5,0"/>
                        <TextBlock x:Name="txtAccountNumber" Text="1010"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20,0,0,0">
                        <TextBlock Text="اسم الحساب:" FontWeight="Bold" Margin="0,0,5,0"/>
                        <TextBlock x:Name="txtAccountName" Text="الصندوق"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="20,0,0,0">
                        <TextBlock Text="نوع الحساب:" FontWeight="Bold" Margin="0,0,5,0"/>
                        <TextBlock x:Name="txtAccountType" Text="الأصول"/>
                    </StackPanel>

                    <StackPanel Grid.Column="3" Orientation="Horizontal" Margin="20,0,0,0">
                        <TextBlock Text="الرصيد:" FontWeight="Bold" Margin="0,0,5,0"/>
                        <TextBlock x:Name="txtAccountBalance" Text="35,000.00 ر.س"/>
                    </StackPanel>
                </Grid>

                <!-- جدول حركات الحساب -->
                <DataGrid Grid.Row="1" x:Name="dgLedgerEntries" AutoGenerateColumns="False" CanUserAddRows="False"
                          Style="{StaticResource DataGridStyle}" Margin="0">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=yyyy-MM-dd}" Width="100"/>
                        <DataGridTextColumn Header="رقم القيد" Binding="{Binding EntryNumber}" Width="100"/>
                        <DataGridTextColumn Header="البيان" Binding="{Binding Description}" Width="*"/>
                        <DataGridTextColumn Header="مدين" Binding="{Binding Debit, StringFormat=N2}" Width="120"/>
                        <DataGridTextColumn Header="دائن" Binding="{Binding Credit, StringFormat=N2}" Width="120"/>
                        <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance, StringFormat=N2}" Width="120"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- شريط الإحصائيات -->
        <Grid Grid.Row="4" Margin="20,0,20,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Border Grid.Column="0" Style="{StaticResource StatCard}" Background="#4CAF50" Margin="0,0,5,0">
                <StackPanel>
                    <TextBlock Text="إجمالي الحركات المدينة" Style="{StaticResource StatTitle}"/>
                    <TextBlock x:Name="txtTotalDebit" Text="0.00 ر.س" Style="{StaticResource StatValue}"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="1" Style="{StaticResource StatCard}" Background="#2196F3" Margin="5,0">
                <StackPanel>
                    <TextBlock Text="إجمالي الحركات الدائنة" Style="{StaticResource StatTitle}"/>
                    <TextBlock x:Name="txtTotalCredit" Text="0.00 ر.س" Style="{StaticResource StatValue}"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="2" Style="{StaticResource StatCard}" Background="#FF9800" Margin="5,0,0,0">
                <StackPanel>
                    <TextBlock Text="الرصيد النهائي" Style="{StaticResource StatTitle}"/>
                    <TextBlock x:Name="txtFinalBalance" Text="0.00 ر.س" Style="{StaticResource StatValue}"/>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Page>
