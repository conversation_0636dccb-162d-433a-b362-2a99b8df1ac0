using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Purchases
{
    /// <summary>
    /// Interaction logic for PurchaseReturnWindow.xaml
    /// </summary>
    public partial class PurchaseReturnWindow : Window
    {
        private ObservableCollection<PurchaseReturnItem> _items;
        private Dictionary<string, SupplierInvoiceDetails> _invoicesData;

        public PurchaseReturnWindow()
        {
            InitializeComponent();
            InitializeInvoicesData();
            LoadSampleData();
        }

        private void InitializeInvoicesData()
        {
            // بيانات تجريبية للفواتير
            _invoicesData = new Dictionary<string, SupplierInvoiceDetails>
            {
                {
                    "PUR-00001", new SupplierInvoiceDetails
                    {
                        SupplierName = "شركة التوريدات العامة",
                        SupplierPhone = "0555123456",
                        InvoiceDate = DateTime.Now.AddDays(-1),
                        Items = new List<PurchaseItem>
                        {
                            new PurchaseItem { Code = "P001", Name = "مواد خام", Unit = "كيلو", Quantity = 50, Price = 15.00m },
                            new PurchaseItem { Code = "P002", Name = "أدوات مكتبية", Unit = "علبة", Quantity = 10, Price = 50.00m }
                        }
                    }
                },
                {
                    "PUR-00002", new SupplierInvoiceDetails
                    {
                        SupplierName = "مؤسسة الإمداد",
                        SupplierPhone = "0555789012",
                        InvoiceDate = DateTime.Now.AddDays(-3),
                        Items = new List<PurchaseItem>
                        {
                            new PurchaseItem { Code = "P003", Name = "معدات", Unit = "قطعة", Quantity = 5, Price = 120.25m },
                            new PurchaseItem { Code = "P004", Name = "قطع غيار", Unit = "قطعة", Quantity = 15, Price = 85.75m }
                        }
                    }
                },
                {
                    "PUR-00003", new SupplierInvoiceDetails
                    {
                        SupplierName = "شركة المواد الأولية",
                        SupplierPhone = "0555456789",
                        InvoiceDate = DateTime.Now.AddDays(-5),
                        Items = new List<PurchaseItem>
                        {
                            new PurchaseItem { Code = "P005", Name = "مواد كيميائية", Unit = "لتر", Quantity = 20, Price = 35.00m },
                            new PurchaseItem { Code = "P006", Name = "مواد تغليف", Unit = "رول", Quantity = 5, Price = 220.00m }
                        }
                    }
                }
            };
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية لأصناف المردود
                _items = new ObservableCollection<PurchaseReturnItem>
                {
                    new PurchaseReturnItem { Code = "P001", Name = "مواد خام", Unit = "كيلو", PurchasedQuantity = 50, ReturnQuantity = 10, Price = 15.00, Total = 150.00 },
                    new PurchaseReturnItem { Code = "P002", Name = "أدوات مكتبية", Unit = "علبة", PurchasedQuantity = 10, ReturnQuantity = 2, Price = 50.00, Total = 100.00 }
                };
                dgItems.ItemsSource = _items;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void cmbOriginalInvoice_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    string invoiceNumber = selectedItem.Content.ToString();

                    if (_invoicesData.ContainsKey(invoiceNumber))
                    {
                        var invoiceDetails = _invoicesData[invoiceNumber];

                        // تعبئة بيانات المورد
                        // تحديث مباشر للعناصر المرئية
                        var supplierTextBoxes = this.FindName("txtSupplier") as TextBox;
                        var phoneTextBoxes = this.FindName("txtSupplierPhone") as TextBox;

                        if (supplierTextBoxes != null)
                            supplierTextBoxes.Text = invoiceDetails.SupplierName;

                        if (phoneTextBoxes != null)
                            phoneTextBoxes.Text = invoiceDetails.SupplierPhone;

                        // تحميل أصناف الفاتورة
                        var returnItems = new ObservableCollection<PurchaseReturnItem>();
                        foreach (var item in invoiceDetails.Items)
                        {
                            returnItems.Add(new PurchaseReturnItem
                            {
                                Code = item.Code,
                                Name = item.Name,
                                Unit = item.Unit,
                                PurchasedQuantity = Convert.ToDouble(item.Quantity),
                                ReturnQuantity = 0,
                                Price = Convert.ToDouble(item.Price),
                                Total = 0
                            });
                        }

                        dgItems.ItemsSource = returnItems;
                        _items = returnItems;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل بيانات الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnAddItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إضافة منتج جديد للمردود
                var newItem = new PurchaseReturnItem
                {
                    Code = "P00" + (_items.Count + 1),
                    Name = "منتج جديد",
                    Unit = "قطعة",
                    PurchasedQuantity = 10,
                    ReturnQuantity = 1,
                    Price = 100,
                    Total = 100
                };

                _items.Add(newItem);
                MessageBox.Show("تمت إضافة المنتج بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة المنتج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعديل المنتج المحدد
                var button = sender as Button;
                var item = button.DataContext as PurchaseReturnItem;

                if (item != null)
                {
                    MessageBox.Show($"تم اختيار المنتج: {item.Name} للتعديل", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تعديل المنتج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDeleteItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حذف المنتج المحدد
                var button = sender as Button;
                var item = button.DataContext as PurchaseReturnItem;

                if (item != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف المنتج: {item.Name}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        _items.Remove(item);
                        MessageBox.Show("تم حذف المنتج بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف المنتج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حفظ المردود
                MessageBox.Show("تم حفظ المردود بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ المردود: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSaveAndPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حفظ وطباعة المردود
                MessageBox.Show("تم حفظ المردود وإرسالها للطباعة", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ وطباعة المردود: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إلغاء المردود
                var result = MessageBox.Show("هل أنت متأكد من إلغاء المردود؟ سيتم فقدان جميع البيانات المدخلة.", "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    this.DialogResult = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class PurchaseReturnItem
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Unit { get; set; }
        public double PurchasedQuantity { get; set; }
        public double ReturnQuantity { get; set; }
        public double Price { get; set; }
        public double Total { get; set; }
    }

    public class PurchaseItem
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Unit { get; set; }
        public decimal Quantity { get; set; }
        public decimal Price { get; set; }
    }

    public class SupplierInvoiceDetails
    {
        public string SupplierName { get; set; }
        public string SupplierPhone { get; set; }
        public DateTime InvoiceDate { get; set; }
        public List<PurchaseItem> Items { get; set; }
    }
}
