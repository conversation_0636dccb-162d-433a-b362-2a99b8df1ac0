using System;
using InjazAcc.Core.Exceptions;

namespace InjazAcc.Services.Helpers
{
    /// <summary>
    /// ملحقات لتسهيل التعامل مع الاستثناءات
    /// </summary>
    public static class ExceptionExtensions
    {
        /// <summary>
        /// معالجة الاستثناء وعرض رسالة مناسبة للمستخدم
        /// </summary>
        /// <param name="exception">الاستثناء المراد معالجته</param>
        /// <param name="showMessageToUser">ما إذا كان يجب عرض رسالة للمستخدم</param>
        /// <returns>رسالة الخطأ المعروضة للمستخدم</returns>
        public static string Handle(this Exception exception, bool showMessageToUser = true)
        {
            return ExceptionHandlingService.HandleExceptionStatic(exception, showMessageToUser);
        }

        /// <summary>
        /// تسجيل الاستثناء في ملف السجل
        /// </summary>
        /// <param name="exception">الاستثناء المراد تسجيله</param>
        public static void Log(this Exception exception)
        {
            ExceptionHandlingService.LogExceptionStatic(exception);
        }

        /// <summary>
        /// تحويل استثناء عام إلى استثناء مخصص للتطبيق
        /// </summary>
        /// <param name="exception">الاستثناء المراد تحويله</param>
        /// <param name="message">رسالة الخطأ المخصصة (اختياري)</param>
        /// <returns>استثناء مخصص للتطبيق</returns>
        public static InjazAccException ToInjazException(this Exception exception, string message = null)
        {
            if (exception is InjazAccException injazException)
            {
                return injazException;
            }

            string errorMessage = message ?? exception.Message;

            // تحديد نوع الاستثناء المناسب بناءً على نوع الاستثناء الأصلي
            if (exception is ArgumentException || exception is FormatException || exception is InvalidOperationException)
            {
                return new ValidationException(errorMessage, ErrorCodes.ValidationFailed);
            }

            if (exception is UnauthorizedAccessException)
            {
                return new AuthorizationException(errorMessage, ErrorCodes.UnauthorizedAccess);
            }

            if (exception is System.Data.Common.DbException)
            {
                return new DatabaseException(errorMessage, ErrorCodes.DatabaseQueryError);
            }

            // استثناء عام إذا لم يتم التعرف على نوع الاستثناء
            return new InjazAccException(errorMessage, "ERR-GEN-002", ErrorSeverity.Error, exception);
        }
    }
}
