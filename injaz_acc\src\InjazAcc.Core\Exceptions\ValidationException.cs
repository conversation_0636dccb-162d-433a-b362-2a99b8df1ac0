using System;
using System.Collections.Generic;

namespace InjazAcc.Core.Exceptions
{
    /// <summary>
    /// استثناء مخصص لأخطاء التحقق من صحة البيانات
    /// </summary>
    public class ValidationException : InjazAccException
    {
        /// <summary>
        /// قائمة بأخطاء التحقق
        /// </summary>
        public Dictionary<string, List<string>> ValidationErrors { get; }

        /// <summary>
        /// إنشاء استثناء جديد بدون رسالة
        /// </summary>
        public ValidationException() : base("البيانات المدخلة غير صحيحة", "ERR-VAL-001")
        {
            ValidationErrors = new Dictionary<string, List<string>>();
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        public ValidationException(string message) : base(message, "ERR-VAL-001")
        {
            ValidationErrors = new Dictionary<string, List<string>>();
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة وقائمة أخطاء
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="validationErrors">قائمة أخطاء التحقق</param>
        public ValidationException(string message, Dictionary<string, List<string>> validationErrors) : base(message, "ERR-VAL-001")
        {
            ValidationErrors = validationErrors ?? new Dictionary<string, List<string>>();
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        public ValidationException(string message, string errorCode) : base(message, errorCode)
        {
            ValidationErrors = new Dictionary<string, List<string>>();
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ وقائمة أخطاء
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="validationErrors">قائمة أخطاء التحقق</param>
        public ValidationException(string message, string errorCode, Dictionary<string, List<string>> validationErrors) : base(message, errorCode)
        {
            ValidationErrors = validationErrors ?? new Dictionary<string, List<string>>();
        }

        /// <summary>
        /// إضافة خطأ تحقق جديد
        /// </summary>
        /// <param name="propertyName">اسم الخاصية</param>
        /// <param name="errorMessage">رسالة الخطأ</param>
        public void AddValidationError(string propertyName, string errorMessage)
        {
            if (!ValidationErrors.ContainsKey(propertyName))
            {
                ValidationErrors[propertyName] = new List<string>();
            }

            ValidationErrors[propertyName].Add(errorMessage);
        }

        /// <summary>
        /// الحصول على رسالة خطأ مفصلة تتضمن جميع أخطاء التحقق
        /// </summary>
        /// <returns>رسالة الخطأ المفصلة</returns>
        public override string GetDetailedMessage()
        {
            var baseMessage = base.GetDetailedMessage();
            
            if (ValidationErrors.Count == 0)
            {
                return baseMessage;
            }

            var detailedMessage = $"{baseMessage}\nأخطاء التحقق:";
            
            foreach (var error in ValidationErrors)
            {
                detailedMessage += $"\n- {error.Key}: {string.Join(", ", error.Value)}";
            }

            return detailedMessage;
        }
    }
}
