using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج الصلاحية في النظام
    /// </summary>
    public class Permission
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Module { get; set; }
        
        // العلاقة مع الأدوار
        public virtual ICollection<RolePermission> RolePermissions { get; set; }
    }
}
