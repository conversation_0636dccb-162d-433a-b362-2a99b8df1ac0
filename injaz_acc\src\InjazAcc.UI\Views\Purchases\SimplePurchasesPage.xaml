<Page x:Class="InjazAcc.UI.Views.Purchases.SimplePurchasesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Purchases"
      mc:Ignorable="d" 
      d:DesignHeight="650" d:DesignWidth="1100"
      Title="صفحة المشتريات">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- عنوان الصفحة -->
        <TextBlock Grid.Row="0" Text="إدارة المشتريات" Style="{StaticResource PageTitle}"/>
        
        <!-- بطاقات الإحصائيات -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- إجمالي المشتريات -->
            <materialDesign:Card Grid.Column="0" Margin="5" Padding="15" Background="{DynamicResource PrimaryHueLightBrush}" UniformCornerRadius="8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <materialDesign:PackIcon Grid.Row="0" Kind="ShoppingOutline" Width="32" Height="32" HorizontalAlignment="Right" Foreground="White"/>
                    <TextBlock Grid.Row="1" Text="إجمالي المشتريات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,10,0,5" Foreground="White"/>
                    <TextBlock Grid.Row="2" Text="18,500 ر.س" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right" Foreground="White"/>
                </Grid>
            </materialDesign:Card>
            
            <!-- عدد الفواتير -->
            <materialDesign:Card Grid.Column="1" Margin="5" Padding="15" Background="#4CAF50" UniformCornerRadius="8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <materialDesign:PackIcon Grid.Row="0" Kind="FileDocumentOutline" Width="32" Height="32" HorizontalAlignment="Right" Foreground="White"/>
                    <TextBlock Grid.Row="1" Text="عدد الفواتير" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,10,0,5" Foreground="White"/>
                    <TextBlock Grid.Row="2" Text="32" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right" Foreground="White"/>
                </Grid>
            </materialDesign:Card>
            
            <!-- إجمالي المردودات -->
            <materialDesign:Card Grid.Column="2" Margin="5" Padding="15" Background="#FF5722" UniformCornerRadius="8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <materialDesign:PackIcon Grid.Row="0" Kind="CartRemove" Width="32" Height="32" HorizontalAlignment="Right" Foreground="White"/>
                    <TextBlock Grid.Row="1" Text="إجمالي المردودات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,10,0,5" Foreground="White"/>
                    <TextBlock Grid.Row="2" Text="1,200 ر.س" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right" Foreground="White"/>
                </Grid>
            </materialDesign:Card>
            
            <!-- صافي المشتريات -->
            <materialDesign:Card Grid.Column="3" Margin="5" Padding="15" Background="#2196F3" UniformCornerRadius="8">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <materialDesign:PackIcon Grid.Row="0" Kind="CashRegister" Width="32" Height="32" HorizontalAlignment="Right" Foreground="White"/>
                    <TextBlock Grid.Row="1" Text="صافي المشتريات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Right" Margin="0,10,0,5" Foreground="White"/>
                    <TextBlock Grid.Row="2" Text="17,300 ر.س" FontSize="20" FontWeight="Bold" HorizontalAlignment="Right" Foreground="White"/>
                </Grid>
            </materialDesign:Card>
        </Grid>
        
        <!-- محتوى الصفحة - جدول الفواتير -->
        <TabControl Grid.Row="2" Style="{StaticResource MaterialDesignTabControl}" FlowDirection="RightToLeft">
            <!-- قائمة الفواتير -->
            <TabItem Header="فواتير المشتريات">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- أدوات البحث والفلترة -->
                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Grid.Column="0" materialDesign:HintAssist.Hint="بحث..." Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,10,0"/>
                        
                        <ComboBox Grid.Column="1" materialDesign:HintAssist.Hint="الحالة" Style="{StaticResource MaterialDesignOutlinedComboBox}" MinWidth="120" Margin="0,0,10,0">
                            <ComboBoxItem Content="الكل"/>
                            <ComboBoxItem Content="مؤكدة"/>
                            <ComboBoxItem Content="مدفوعة"/>
                            <ComboBoxItem Content="مدفوعة جزئياً"/>
                            <ComboBoxItem Content="ملغاة"/>
                        </ComboBox>
                        
                        <DatePicker Grid.Column="2" materialDesign:HintAssist.Hint="من تاريخ" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Width="120" Margin="0,0,10,0"/>
                        
                        <DatePicker Grid.Column="3" materialDesign:HintAssist.Hint="إلى تاريخ" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Width="120"/>
                    </Grid>
                    
                    <!-- جدول الفواتير -->
                    <DataGrid Grid.Row="1" x:Name="dgPurchases" Style="{StaticResource DataGridStyle}" AutoGenerateColumns="False" IsReadOnly="True" MouseDoubleClick="dgPurchases_MouseDoubleClick">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="100"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding InvoiceDate, StringFormat=yyyy-MM-dd}" Width="120"/>
                            <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="200"/>
                            <DataGridTextColumn Header="المبلغ الإجمالي" Binding="{Binding TotalAmount, StringFormat=N2}" Width="120"/>
                            <DataGridTextColumn Header="المبلغ المدفوع" Binding="{Binding PaidAmount, StringFormat=N2}" Width="120"/>
                            <DataGridTextColumn Header="المبلغ المتبقي" Binding="{Binding RemainingAmount, StringFormat=N2}" Width="120"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="عرض" Click="btnViewInvoice_Click">
                                                <materialDesign:PackIcon Kind="Eye" Width="20" Height="20"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="تعديل" Click="btnEditInvoice_Click">
                                                <materialDesign:PackIcon Kind="Pencil" Width="20" Height="20"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="طباعة" Click="btnPrintInvoice_Click">
                                                <materialDesign:PackIcon Kind="Printer" Width="20" Height="20"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="حذف" Click="btnDeleteInvoice_Click">
                                                <materialDesign:PackIcon Kind="Delete" Width="20" Height="20"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            
            <!-- قائمة المردودات -->
            <TabItem Header="مردودات المشتريات">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- أدوات البحث والفلترة -->
                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBox Grid.Column="0" materialDesign:HintAssist.Hint="بحث..." Style="{StaticResource MaterialDesignOutlinedTextBox}" Margin="0,0,10,0"/>
                        
                        <DatePicker Grid.Column="1" materialDesign:HintAssist.Hint="من تاريخ" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Width="120" Margin="0,0,10,0"/>
                        
                        <DatePicker Grid.Column="2" materialDesign:HintAssist.Hint="إلى تاريخ" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Width="120"/>
                    </Grid>
                    
                    <!-- جدول المردودات -->
                    <DataGrid Grid.Row="1" x:Name="dgReturns" Style="{StaticResource DataGridStyle}" AutoGenerateColumns="False" IsReadOnly="True" MouseDoubleClick="dgReturns_MouseDoubleClick">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم المردود" Binding="{Binding InvoiceNumber}" Width="100"/>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding InvoiceDate, StringFormat=yyyy-MM-dd}" Width="120"/>
                            <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="200"/>
                            <DataGridTextColumn Header="رقم الفاتورة الأصلية" Binding="{Binding OriginalInvoiceNumber}" Width="150"/>
                            <DataGridTextColumn Header="المبلغ الإجمالي" Binding="{Binding TotalAmount, StringFormat=N2}" Width="120"/>
                            <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                            <DataGridTemplateColumn Header="الإجراءات" Width="*">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="عرض" Click="btnViewReturn_Click">
                                                <materialDesign:PackIcon Kind="Eye" Width="20" Height="20"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="طباعة" Click="btnPrintReturn_Click">
                                                <materialDesign:PackIcon Kind="Printer" Width="20" Height="20"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            
            <!-- تقارير المشتريات -->
            <TabItem Header="تقارير المشتريات">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- أدوات التقارير -->
                    <Grid Grid.Row="0" Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <ComboBox Grid.Column="0" materialDesign:HintAssist.Hint="نوع التقرير" Style="{StaticResource MaterialDesignOutlinedComboBox}" MinWidth="150" Margin="0,0,10,0">
                            <ComboBoxItem Content="مشتريات حسب الموردين"/>
                            <ComboBoxItem Content="مشتريات حسب المنتجات"/>
                            <ComboBoxItem Content="مشتريات حسب الفترة"/>
                            <ComboBoxItem Content="مردودات المشتريات"/>
                        </ComboBox>
                        
                        <DatePicker Grid.Column="1" materialDesign:HintAssist.Hint="من تاريخ" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Width="120" Margin="0,0,10,0"/>
                        
                        <DatePicker Grid.Column="2" materialDesign:HintAssist.Hint="إلى تاريخ" Style="{StaticResource MaterialDesignOutlinedDatePicker}" Width="120" Margin="0,0,10,0"/>
                        
                        <Button Grid.Column="4" Content="عرض التقرير" Style="{StaticResource ActionButton}" Click="btnShowReport_Click"/>
                    </Grid>
                    
                    <!-- محتوى التقرير -->
                    <Border Grid.Row="1" BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="1">
                        <TextBlock Text="اختر نوع التقرير وحدد الفترة ثم اضغط على عرض التقرير" 
                                   HorizontalAlignment="Center" VerticalAlignment="Center" 
                                   FontSize="16" Foreground="Gray"/>
                    </Border>
                </Grid>
            </TabItem>
        </TabControl>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="btnNewPurchase" Style="{StaticResource ActionButton}" Margin="10,0" Click="btnNewPurchase_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="CartPlus" Width="20" Height="20" VerticalAlignment="Center"/>
                    <TextBlock Text="فاتورة مشتريات جديدة" Margin="8,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            
            <Button x:Name="btnNewReturn" Style="{StaticResource ActionButton}" Margin="10,0" Click="btnNewReturn_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="CartRemove" Width="20" Height="20" VerticalAlignment="Center"/>
                    <TextBlock Text="مردودات مشتريات" Margin="8,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
            
            <Button x:Name="btnPurchasesReport" Style="{StaticResource ActionButton}" Margin="10,0" Click="btnPurchasesReport_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="FileDocumentOutline" Width="20" Height="20" VerticalAlignment="Center"/>
                    <TextBlock Text="تقرير المشتريات" Margin="8,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </StackPanel>
    </Grid>
</Page>
