using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using InjazAcc.Core.Interfaces;
using InjazAcc.Core.Models;

namespace InjazAcc.Services
{
    /// <summary>
    /// خدمة القيود المحاسبية
    /// </summary>
    public class AccountingService : IAccountingService
    {
        private readonly IUnitOfWork _unitOfWork;

        public AccountingService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// إنشاء قيد محاسبي لفاتورة مبيعات
        /// </summary>
        public async Task<JournalEntry> CreateSalesInvoiceEntryAsync(Invoice invoice, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (invoice == null)
                    throw new ArgumentNullException(nameof(invoice));

                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"INV-{invoice.InvoiceNumber}",
                    EntryDate = invoice.InvoiceDate,
                    Type = JournalEntryType.Invoice,
                    Description = $"قيد فاتورة مبيعات رقم {invoice.InvoiceNumber}",
                    UserId = userId,
                    InvoiceId = invoice.Id,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };

                // الحصول على معرفات الحسابات
                int salesAccountId = await GetSalesAccountIdAsync();
                int customersAccountId = await GetCustomersAccountIdAsync();
                int treasuryAccountId = await GetTreasuryAccountIdAsync();
                int inventoryAccountId = await GetInventoryAccountIdAsync();
                int costOfSalesAccountId = await GetCostOfSalesAccountIdAsync();

                // إضافة عناصر القيد

                // 1. مدين: حساب العملاء أو الخزينة (حسب طريقة الدفع)
                if (invoice.PaidAmount > 0)
                {
                    // مدين: حساب الخزينة (بقيمة المبلغ المدفوع)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = treasuryAccountId,
                        Description = $"تحصيل نقدي من فاتورة مبيعات رقم {invoice.InvoiceNumber}",
                        DebitAmount = invoice.PaidAmount,
                        CreditAmount = 0
                    });
                }

                if (invoice.RemainingAmount > 0)
                {
                    // مدين: حساب العملاء (بقيمة المبلغ المتبقي)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = customersAccountId,
                        Description = $"ذمم مدينة من فاتورة مبيعات رقم {invoice.InvoiceNumber}",
                        DebitAmount = invoice.RemainingAmount,
                        CreditAmount = 0
                    });
                }

                // 2. دائن: حساب المبيعات (بإجمالي قيمة الفاتورة)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = salesAccountId,
                    Description = $"إيراد مبيعات من فاتورة رقم {invoice.InvoiceNumber}",
                    DebitAmount = 0,
                    CreditAmount = invoice.TotalAmount
                });

                // 3. مدين: حساب تكلفة المبيعات (بتكلفة البضاعة المباعة)
                decimal costOfGoods = CalculateCostOfGoods(invoice);
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = costOfSalesAccountId,
                    Description = $"تكلفة بضاعة مباعة من فاتورة رقم {invoice.InvoiceNumber}",
                    DebitAmount = costOfGoods,
                    CreditAmount = 0
                });

                // 4. دائن: حساب المخزون (بتكلفة البضاعة المباعة)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = inventoryAccountId,
                    Description = $"تخفيض المخزون من فاتورة مبيعات رقم {invoice.InvoiceNumber}",
                    DebitAmount = 0,
                    CreditAmount = costOfGoods
                });

                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);

                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();

                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد فاتورة المبيعات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// حساب تكلفة البضاعة المباعة
        /// </summary>
        private decimal CalculateCostOfGoods(Invoice invoice)
        {
            // في التطبيق الحقيقي، سيتم حساب تكلفة البضاعة المباعة بناءً على سعر التكلفة للمنتجات
            // هنا نفترض أن تكلفة البضاعة المباعة هي 70% من قيمة الفاتورة
            return invoice.TotalAmount * 0.7m;
        }

        /// <summary>
        /// إنشاء قيد محاسبي لفاتورة مشتريات
        /// </summary>
        public async Task<JournalEntry> CreatePurchaseInvoiceEntryAsync(Invoice invoice, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (invoice == null)
                    throw new ArgumentNullException(nameof(invoice));

                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"INV-{invoice.InvoiceNumber}",
                    EntryDate = invoice.InvoiceDate,
                    Type = JournalEntryType.Invoice,
                    Description = $"قيد فاتورة مشتريات رقم {invoice.InvoiceNumber}",
                    UserId = userId,
                    InvoiceId = invoice.Id,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };

                // الحصول على معرفات الحسابات
                int suppliersAccountId = await GetSuppliersAccountIdAsync();
                int treasuryAccountId = await GetTreasuryAccountIdAsync();
                int inventoryAccountId = await GetInventoryAccountIdAsync();

                // إضافة عناصر القيد

                // 1. مدين: حساب المخزون (بإجمالي قيمة الفاتورة)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = inventoryAccountId,
                    Description = $"إضافة للمخزون من فاتورة مشتريات رقم {invoice.InvoiceNumber}",
                    DebitAmount = invoice.TotalAmount,
                    CreditAmount = 0
                });

                // 2. دائن: حساب الموردين أو الخزينة (حسب طريقة الدفع)
                if (invoice.PaidAmount > 0)
                {
                    // دائن: حساب الخزينة (بقيمة المبلغ المدفوع)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = treasuryAccountId,
                        Description = $"دفع نقدي لفاتورة مشتريات رقم {invoice.InvoiceNumber}",
                        DebitAmount = 0,
                        CreditAmount = invoice.PaidAmount
                    });
                }

                if (invoice.RemainingAmount > 0)
                {
                    // دائن: حساب الموردين (بقيمة المبلغ المتبقي)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = suppliersAccountId,
                        Description = $"ذمم دائنة لفاتورة مشتريات رقم {invoice.InvoiceNumber}",
                        DebitAmount = 0,
                        CreditAmount = invoice.RemainingAmount
                    });
                }

                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);

                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();

                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد فاتورة المشتريات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء قيد محاسبي لمردودات مبيعات
        /// </summary>
        public async Task<JournalEntry> CreateSalesReturnEntryAsync(Invoice invoice, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (invoice == null)
                    throw new ArgumentNullException(nameof(invoice));

                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"RET-{invoice.InvoiceNumber}",
                    EntryDate = invoice.InvoiceDate,
                    Type = JournalEntryType.Invoice,
                    Description = $"قيد مردودات مبيعات رقم {invoice.InvoiceNumber}",
                    UserId = userId,
                    InvoiceId = invoice.Id,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };

                // الحصول على معرفات الحسابات
                int salesAccountId = await GetSalesAccountIdAsync();
                int customersAccountId = await GetCustomersAccountIdAsync();
                int treasuryAccountId = await GetTreasuryAccountIdAsync();
                int inventoryAccountId = await GetInventoryAccountIdAsync();
                int costOfSalesAccountId = await GetCostOfSalesAccountIdAsync();

                // إضافة عناصر القيد

                // 1. مدين: حساب المبيعات (بإجمالي قيمة المردودات)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = salesAccountId,
                    Description = $"تخفيض إيراد المبيعات من مردودات رقم {invoice.InvoiceNumber}",
                    DebitAmount = invoice.TotalAmount,
                    CreditAmount = 0
                });

                // 2. دائن: حساب العملاء أو الخزينة (حسب طريقة الدفع)
                if (invoice.PaidAmount > 0)
                {
                    // دائن: حساب الخزينة (بقيمة المبلغ المدفوع)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = treasuryAccountId,
                        Description = $"دفع نقدي لمردودات مبيعات رقم {invoice.InvoiceNumber}",
                        DebitAmount = 0,
                        CreditAmount = invoice.PaidAmount
                    });
                }

                if (invoice.RemainingAmount > 0)
                {
                    // دائن: حساب العملاء (بقيمة المبلغ المتبقي)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = customersAccountId,
                        Description = $"تخفيض ذمم مدينة من مردودات مبيعات رقم {invoice.InvoiceNumber}",
                        DebitAmount = 0,
                        CreditAmount = invoice.RemainingAmount
                    });
                }

                // 3. مدين: حساب المخزون (بتكلفة البضاعة المرتجعة)
                decimal costOfGoods = CalculateCostOfGoods(invoice);
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = inventoryAccountId,
                    Description = $"إضافة للمخزون من مردودات مبيعات رقم {invoice.InvoiceNumber}",
                    DebitAmount = costOfGoods,
                    CreditAmount = 0
                });

                // 4. دائن: حساب تكلفة المبيعات (بتكلفة البضاعة المرتجعة)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = costOfSalesAccountId,
                    Description = $"تخفيض تكلفة البضاعة المباعة من مردودات رقم {invoice.InvoiceNumber}",
                    DebitAmount = 0,
                    CreditAmount = costOfGoods
                });

                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);

                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();

                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد مردودات المبيعات: {ex.Message}");
                throw;
            }
        }
        /// <summary>
        /// إنشاء قيد محاسبي لمردودات مشتريات
        /// </summary>
        public async Task<JournalEntry> CreatePurchaseReturnEntryAsync(Invoice invoice, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (invoice == null)
                    throw new ArgumentNullException(nameof(invoice));

                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"RET-{invoice.InvoiceNumber}",
                    EntryDate = invoice.InvoiceDate,
                    Type = JournalEntryType.Invoice,
                    Description = $"قيد مردودات مشتريات رقم {invoice.InvoiceNumber}",
                    UserId = userId,
                    InvoiceId = invoice.Id,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };

                // الحصول على معرفات الحسابات
                int suppliersAccountId = await GetSuppliersAccountIdAsync();
                int treasuryAccountId = await GetTreasuryAccountIdAsync();
                int inventoryAccountId = await GetInventoryAccountIdAsync();

                // إضافة عناصر القيد

                // 1. مدين: حساب الموردين أو الخزينة (حسب طريقة الدفع)
                if (invoice.PaidAmount > 0)
                {
                    // مدين: حساب الخزينة (بقيمة المبلغ المدفوع)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = treasuryAccountId,
                        Description = $"استلام نقدي من مردودات مشتريات رقم {invoice.InvoiceNumber}",
                        DebitAmount = invoice.PaidAmount,
                        CreditAmount = 0
                    });
                }

                if (invoice.RemainingAmount > 0)
                {
                    // مدين: حساب الموردين (بقيمة المبلغ المتبقي)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = suppliersAccountId,
                        Description = $"تخفيض ذمم دائنة من مردودات مشتريات رقم {invoice.InvoiceNumber}",
                        DebitAmount = invoice.RemainingAmount,
                        CreditAmount = 0
                    });
                }

                // 2. دائن: حساب المخزون (بإجمالي قيمة المردودات)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = inventoryAccountId,
                    Description = $"تخفيض المخزون من مردودات مشتريات رقم {invoice.InvoiceNumber}",
                    DebitAmount = 0,
                    CreditAmount = invoice.TotalAmount
                });

                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);

                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();

                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد مردودات المشتريات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء قيد محاسبي لدفعة من عميل
        /// </summary>
        public async Task<JournalEntry> CreateCustomerPaymentEntryAsync(Payment payment, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (payment == null)
                    throw new ArgumentNullException(nameof(payment));

                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"PAY-{payment.ReferenceNumber}",
                    EntryDate = payment.PaymentDate,
                    Type = JournalEntryType.Payment,
                    Description = $"قيد دفعة من عميل رقم {payment.ReferenceNumber}",
                    UserId = userId,
                    PaymentId = payment.Id,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };

                // الحصول على معرفات الحسابات
                int customersAccountId = await GetCustomersAccountIdAsync();
                int treasuryAccountId = await GetTreasuryAccountIdAsync();

                // إضافة عناصر القيد

                // 1. مدين: حساب الخزينة (بقيمة الدفعة)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = treasuryAccountId,
                    Description = $"استلام دفعة من عميل رقم {payment.ReferenceNumber}",
                    DebitAmount = payment.Amount,
                    CreditAmount = 0
                });

                // 2. دائن: حساب العملاء (بقيمة الدفعة)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = customersAccountId,
                    Description = $"تخفيض ذمم مدينة من دفعة عميل رقم {payment.ReferenceNumber}",
                    DebitAmount = 0,
                    CreditAmount = payment.Amount
                });

                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);

                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();

                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد دفعة من عميل: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء قيد محاسبي لدفعة لمورد
        /// </summary>
        public async Task<JournalEntry> CreateSupplierPaymentEntryAsync(Payment payment, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (payment == null)
                    throw new ArgumentNullException(nameof(payment));

                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"PAY-{payment.ReferenceNumber}",
                    EntryDate = payment.PaymentDate,
                    Type = JournalEntryType.Payment,
                    Description = $"قيد دفعة لمورد رقم {payment.ReferenceNumber}",
                    UserId = userId,
                    PaymentId = payment.Id,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };

                // الحصول على معرفات الحسابات
                int suppliersAccountId = await GetSuppliersAccountIdAsync();
                int treasuryAccountId = await GetTreasuryAccountIdAsync();

                // إضافة عناصر القيد

                // 1. مدين: حساب الموردين (بقيمة الدفعة)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = suppliersAccountId,
                    Description = $"تخفيض ذمم دائنة من دفعة لمورد رقم {payment.ReferenceNumber}",
                    DebitAmount = payment.Amount,
                    CreditAmount = 0
                });

                // 2. دائن: حساب الخزينة (بقيمة الدفعة)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = treasuryAccountId,
                    Description = $"دفع مبلغ لمورد رقم {payment.ReferenceNumber}",
                    DebitAmount = 0,
                    CreditAmount = payment.Amount
                });

                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);

                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();

                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد دفعة لمورد: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على معرف الحساب حسب النوع
        /// </summary>
        public async Task<int> GetAccountIdByTypeAsync(AccountType accountType)
        {
            // البحث عن الحساب حسب النوع
            var account = await _unitOfWork.Accounts.FirstOrDefaultAsync(a => a.Type == accountType && a.IsActive);

            // إذا لم يتم العثور على الحساب، نرجع الحساب الافتراضي
            if (account == null)
            {
                // في التطبيق الحقيقي، يجب إنشاء حساب افتراضي أو رمي استثناء
                return 1; // قيمة افتراضية للتجربة
            }

            return account.Id;
        }

        /// <summary>
        /// الحصول على معرف حساب الخزينة
        /// </summary>
        public async Task<int> GetTreasuryAccountIdAsync()
        {
            // البحث عن إعدادات الحسابات
            var settings = await _unitOfWork.AccountingSettings.FirstOrDefaultAsync(s => true);

            // إذا لم يتم العثور على الإعدادات، نرجع الحساب الافتراضي
            if (settings == null)
            {
                // في التطبيق الحقيقي، يجب إنشاء إعدادات افتراضية أو رمي استثناء
                return await GetAccountIdByTypeAsync(AccountType.Asset);
            }

            return settings.TreasuryAccountId;
        }

        /// <summary>
        /// الحصول على معرف حساب المخزون
        /// </summary>
        public async Task<int> GetInventoryAccountIdAsync()
        {
            var settings = await _unitOfWork.AccountingSettings.FirstOrDefaultAsync(s => true);

            if (settings == null)
            {
                return await GetAccountIdByTypeAsync(AccountType.Asset);
            }

            return settings.InventoryAccountId;
        }

        /// <summary>
        /// الحصول على معرف حساب المبيعات
        /// </summary>
        public async Task<int> GetSalesAccountIdAsync()
        {
            var settings = await _unitOfWork.AccountingSettings.FirstOrDefaultAsync(s => true);

            if (settings == null)
            {
                return await GetAccountIdByTypeAsync(AccountType.Revenue);
            }

            return settings.SalesAccountId;
        }

        /// <summary>
        /// الحصول على معرف حساب تكلفة المبيعات
        /// </summary>
        public async Task<int> GetCostOfSalesAccountIdAsync()
        {
            var settings = await _unitOfWork.AccountingSettings.FirstOrDefaultAsync(s => true);

            if (settings == null)
            {
                return await GetAccountIdByTypeAsync(AccountType.CostOfSales);
            }

            return settings.CostOfSalesAccountId;
        }

        /// <summary>
        /// الحصول على معرف حساب العملاء
        /// </summary>
        public async Task<int> GetCustomersAccountIdAsync()
        {
            var settings = await _unitOfWork.AccountingSettings.FirstOrDefaultAsync(s => true);

            if (settings == null)
            {
                return await GetAccountIdByTypeAsync(AccountType.Asset);
            }

            return settings.CustomersAccountId;
        }

        /// <summary>
        /// الحصول على معرف حساب الموردين
        /// </summary>
        public async Task<int> GetSuppliersAccountIdAsync()
        {
            var settings = await _unitOfWork.AccountingSettings.FirstOrDefaultAsync(s => true);

            if (settings == null)
            {
                return await GetAccountIdByTypeAsync(AccountType.Liability);
            }

            return settings.SuppliersAccountId;
        }

        /// <summary>
        /// إنشاء قيد محاسبي لمصروف
        /// </summary>
        public async Task<JournalEntry> CreateExpenseEntryAsync(Payment payment, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (payment == null)
                    throw new ArgumentNullException(nameof(payment));

                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"EXP-{payment.ReferenceNumber}",
                    EntryDate = payment.PaymentDate,
                    Type = JournalEntryType.Payment,
                    Description = $"قيد مصروف رقم {payment.ReferenceNumber}",
                    UserId = userId,
                    PaymentId = payment.Id,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };

                // الحصول على معرفات الحسابات
                int treasuryAccountId = await GetTreasuryAccountIdAsync();

                // إضافة عناصر القيد

                // 1. مدين: حساب المصروف (بقيمة المصروف)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = payment.AccountId,
                    Description = $"مصروف رقم {payment.ReferenceNumber}",
                    DebitAmount = payment.Amount,
                    CreditAmount = 0
                });

                // 2. دائن: حساب الخزينة (بقيمة المصروف)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = treasuryAccountId,
                    Description = $"دفع مصروف رقم {payment.ReferenceNumber}",
                    DebitAmount = 0,
                    CreditAmount = payment.Amount
                });

                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);

                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();

                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد مصروف: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء قيد محاسبي لإيراد
        /// </summary>
        public async Task<JournalEntry> CreateRevenueEntryAsync(Payment payment, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (payment == null)
                    throw new ArgumentNullException(nameof(payment));

                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"REV-{payment.ReferenceNumber}",
                    EntryDate = payment.PaymentDate,
                    Type = JournalEntryType.Payment,
                    Description = $"قيد إيراد رقم {payment.ReferenceNumber}",
                    UserId = userId,
                    PaymentId = payment.Id,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };

                // الحصول على معرفات الحسابات
                int treasuryAccountId = await GetTreasuryAccountIdAsync();

                // إضافة عناصر القيد

                // 1. مدين: حساب الخزينة (بقيمة الإيراد)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = treasuryAccountId,
                    Description = $"استلام إيراد رقم {payment.ReferenceNumber}",
                    DebitAmount = payment.Amount,
                    CreditAmount = 0
                });

                // 2. دائن: حساب الإيراد (بقيمة الإيراد)
                journalEntry.Items.Add(new JournalEntryItem
                {
                    AccountId = payment.AccountId,
                    Description = $"إيراد رقم {payment.ReferenceNumber}",
                    DebitAmount = 0,
                    CreditAmount = payment.Amount
                });

                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);

                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();

                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد إيراد: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء قيد محاسبي لتسوية مخزون
        /// </summary>
        public async Task<JournalEntry> CreateInventoryAdjustmentEntryAsync(InventoryTransfer adjustment, int userId)
        {
            try
            {
                // التحقق من صحة البيانات
                if (adjustment == null)
                    throw new ArgumentNullException(nameof(adjustment));

                // إنشاء القيد المحاسبي
                var journalEntry = new JournalEntry
                {
                    ReferenceNumber = $"ADJ-{adjustment.ReferenceNumber}",
                    EntryDate = adjustment.TransferDate,
                    Type = JournalEntryType.InventoryAdjustment,
                    Description = $"قيد تسوية مخزون رقم {adjustment.ReferenceNumber}",
                    UserId = userId,
                    IsPosted = true,
                    PostedDate = DateTime.Now,
                    Items = new List<JournalEntryItem>()
                };

                // الحصول على معرفات الحسابات
                int inventoryAccountId = await GetInventoryAccountIdAsync();

                // حساب قيمة التسوية
                decimal adjustmentValue = CalculateAdjustmentValue(adjustment);

                // إضافة عناصر القيد حسب نوع التسوية (زيادة أو نقصان)
                if (adjustmentValue > 0)
                {
                    // زيادة في المخزون

                    // 1. مدين: حساب المخزون (بقيمة الزيادة)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = inventoryAccountId,
                        Description = $"زيادة في المخزون من تسوية رقم {adjustment.ReferenceNumber}",
                        DebitAmount = adjustmentValue,
                        CreditAmount = 0
                    });

                    // 2. دائن: حساب تسويات المخزون (بقيمة الزيادة)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = await GetAccountIdByTypeAsync(AccountType.Revenue),
                        Description = $"إيراد من تسوية مخزون رقم {adjustment.ReferenceNumber}",
                        DebitAmount = 0,
                        CreditAmount = adjustmentValue
                    });
                }
                else if (adjustmentValue < 0)
                {
                    // نقصان في المخزون

                    // 1. مدين: حساب تسويات المخزون (بقيمة النقصان)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = await GetAccountIdByTypeAsync(AccountType.Expense),
                        Description = $"مصروف من تسوية مخزون رقم {adjustment.ReferenceNumber}",
                        DebitAmount = Math.Abs(adjustmentValue),
                        CreditAmount = 0
                    });

                    // 2. دائن: حساب المخزون (بقيمة النقصان)
                    journalEntry.Items.Add(new JournalEntryItem
                    {
                        AccountId = inventoryAccountId,
                        Description = $"نقصان في المخزون من تسوية رقم {adjustment.ReferenceNumber}",
                        DebitAmount = 0,
                        CreditAmount = Math.Abs(adjustmentValue)
                    });
                }

                // حساب إجماليات القيد
                journalEntry.TotalDebit = journalEntry.Items.Sum(i => i.DebitAmount);
                journalEntry.TotalCredit = journalEntry.Items.Sum(i => i.CreditAmount);

                // حفظ القيد في قاعدة البيانات
                await _unitOfWork.JournalEntries.AddAsync(journalEntry);
                await _unitOfWork.CompleteAsync();

                return journalEntry;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                Console.WriteLine($"خطأ في إنشاء قيد تسوية مخزون: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// حساب قيمة تسوية المخزون
        /// </summary>
        private decimal CalculateAdjustmentValue(InventoryTransfer adjustment)
        {
            // في التطبيق الحقيقي، سيتم حساب قيمة التسوية بناءً على قيمة المنتجات
            // هنا نفترض قيمة افتراضية للتجربة
            return 1000;
        }
    }
}
