namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج عنصر في القيد المحاسبي
    /// </summary>
    public class JournalEntryItem
    {
        public int Id { get; set; }
        public int JournalEntryId { get; set; }
        public int AccountId { get; set; }
        public string Description { get; set; }
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
        
        // العلاقات
        public virtual JournalEntry JournalEntry { get; set; }
        public virtual Account Account { get; set; }
    }
}
