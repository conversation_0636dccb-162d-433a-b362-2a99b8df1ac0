using System;

namespace InjazAcc.Core.Exceptions
{
    /// <summary>
    /// استثناء مخصص لأخطاء التفويض
    /// </summary>
    public class AuthorizationException : InjazAccException
    {
        /// <summary>
        /// إنشاء استثناء جديد بدون رسالة
        /// </summary>
        public AuthorizationException() : base("ليس لديك صلاحية للوصول إلى هذه الوظيفة", "ERR-AUTHZ-001")
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        public AuthorizationException(string message) : base(message, "ERR-AUTHZ-001")
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        public AuthorizationException(string message, string errorCode) : base(message, errorCode)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public AuthorizationException(string message, Exception innerException) : base(message, "ERR-AUTHZ-001", innerException)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public AuthorizationException(string message, string errorCode, Exception innerException) : base(message, errorCode, innerException)
        {
        }
    }
}
