using System;
using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج الشريك في النظام
    /// </summary>
    public class Partner
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public decimal SharePercentage { get; set; }
        public decimal InitialCapital { get; set; }
        public DateTime JoinDate { get; set; }
        public DateTime? LeaveDate { get; set; }
        public bool IsActive { get; set; }
        public string Notes { get; set; }
        
        // العلاقة مع حساب الشريك
        public int AccountId { get; set; }
        public virtual Account Account { get; set; }
        
        // العلاقة مع توزيعات الأرباح
        public virtual ICollection<ProfitDistribution> ProfitDistributions { get; set; }
    }
}
