using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Accounts
{
    /// <summary>
    /// Interaction logic for LedgerPage.xaml
    /// </summary>
    public partial class LedgerPage : Page
    {
        private Dictionary<string, AccountInfo> _accountsInfo;
        private Dictionary<string, ObservableCollection<LedgerEntry>> _accountEntries;

        public LedgerPage()
        {
            InitializeComponent();

            // تعيين التواريخ الافتراضية (الشهر الحالي)
            dpFromDate.SelectedDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            dpToDate.SelectedDate = DateTime.Now;

            // اختيار الحساب الافتراضي
            cmbAccount.SelectedIndex = 0;

            // تحميل البيانات التجريبية
            LoadSampleData();

            // عرض بيانات الحساب المختار
            UpdateAccountInfo();
            UpdateLedgerEntries();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية لمعلومات الحسابات
                _accountsInfo = new Dictionary<string, AccountInfo>
                {
                    { "1010", new AccountInfo { AccountNumber = "1010", AccountName = "الصندوق", AccountType = "الأصول", Balance = 35000 } },
                    { "1020", new AccountInfo { AccountNumber = "1020", AccountName = "البنك", AccountType = "الأصول", Balance = 120000 } },
                    { "1030", new AccountInfo { AccountNumber = "1030", AccountName = "المخزون", AccountType = "الأصول", Balance = 180000 } },
                    { "1040", new AccountInfo { AccountNumber = "1040", AccountName = "العملاء", AccountType = "الأصول", Balance = 65000 } },
                    { "1050", new AccountInfo { AccountNumber = "1050", AccountName = "أثاث ومعدات", AccountType = "الأصول", Balance = 120000 } },
                    { "2010", new AccountInfo { AccountNumber = "2010", AccountName = "الموردين", AccountType = "الخصوم", Balance = 75000 } },
                    { "2020", new AccountInfo { AccountNumber = "2020", AccountName = "قروض قصيرة الأجل", AccountType = "الخصوم", Balance = 100000 } },
                    { "2030", new AccountInfo { AccountNumber = "2030", AccountName = "مصروفات مستحقة", AccountType = "الخصوم", Balance = 25000 } },
                    { "3010", new AccountInfo { AccountNumber = "3010", AccountName = "رأس المال", AccountType = "حقوق الملكية", Balance = 350000 } },
                    { "3020", new AccountInfo { AccountNumber = "3020", AccountName = "الأرباح المحتجزة", AccountType = "حقوق الملكية", Balance = 25000 } },
                    { "4010", new AccountInfo { AccountNumber = "4010", AccountName = "المبيعات", AccountType = "الإيرادات", Balance = 250000 } },
                    { "4020", new AccountInfo { AccountNumber = "4020", AccountName = "إيرادات أخرى", AccountType = "الإيرادات", Balance = 15000 } },
                    { "5010", new AccountInfo { AccountNumber = "5010", AccountName = "تكلفة المبيعات", AccountType = "المصروفات", Balance = 150000 } },
                    { "5020", new AccountInfo { AccountNumber = "5020", AccountName = "رواتب وأجور", AccountType = "المصروفات", Balance = 85000 } },
                    { "5030", new AccountInfo { AccountNumber = "5030", AccountName = "إيجارات", AccountType = "المصروفات", Balance = 35000 } },
                    { "5040", new AccountInfo { AccountNumber = "5040", AccountName = "مصروفات عمومية وإدارية", AccountType = "المصروفات", Balance = 50000 } }
                };

                // بيانات تجريبية لحركات الحسابات
                _accountEntries = new Dictionary<string, ObservableCollection<LedgerEntry>>();

                // حركات حساب الصندوق
                _accountEntries["1010"] = new ObservableCollection<LedgerEntry>
                {
                    new LedgerEntry { Date = DateTime.Now.AddDays(-25), EntryNumber = "J001", Description = "رصيد أول المدة", Debit = 50000, Credit = 0, Balance = 50000 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-20), EntryNumber = "J005", Description = "دفع إيجار المحل", Debit = 0, Credit = 5000, Balance = 45000 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-18), EntryNumber = "J008", Description = "تحصيل من العميل: شركة الأمل", Debit = 8000, Credit = 0, Balance = 53000 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-15), EntryNumber = "J010", Description = "دفع للمورد: شركة التوريدات العامة", Debit = 0, Credit = 12000, Balance = 41000 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-12), EntryNumber = "J012", Description = "إيراد من خدمات استشارية", Debit = 7500, Credit = 0, Balance = 48500 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-10), EntryNumber = "J015", Description = "مصروفات كهرباء وماء", Debit = 0, Credit = 1800, Balance = 46700 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-8), EntryNumber = "J018", Description = "تحصيل من العميل: مؤسسة النور", Debit = 6500, Credit = 0, Balance = 53200 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-5), EntryNumber = "J020", Description = "رواتب الموظفين", Debit = 0, Credit = 18000, Balance = 35200 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-3), EntryNumber = "J022", Description = "دفع للمورد: مؤسسة الإمداد", Debit = 0, Credit = 9500, Balance = 25700 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-1), EntryNumber = "J025", Description = "إيراد من مبيعات نقدية", Debit = 12500, Credit = 0, Balance = 38200 },
                    new LedgerEntry { Date = DateTime.Now, EntryNumber = "J028", Description = "مصروفات نثرية", Debit = 0, Credit = 3200, Balance = 35000 }
                };

                // حركات حساب البنك
                _accountEntries["1020"] = new ObservableCollection<LedgerEntry>
                {
                    new LedgerEntry { Date = DateTime.Now.AddDays(-30), EntryNumber = "J001", Description = "رصيد أول المدة", Debit = 150000, Credit = 0, Balance = 150000 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-28), EntryNumber = "J003", Description = "إيداع نقدي", Debit = 25000, Credit = 0, Balance = 175000 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-25), EntryNumber = "J006", Description = "سحب نقدي", Debit = 0, Credit = 15000, Balance = 160000 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-20), EntryNumber = "J009", Description = "تحويل من العميل: شركة الصفا", Debit = 18000, Credit = 0, Balance = 178000 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-15), EntryNumber = "J013", Description = "دفع قسط القرض", Debit = 0, Credit = 25000, Balance = 153000 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-10), EntryNumber = "J016", Description = "تحويل للمورد: شركة المواد الأولية", Debit = 0, Credit = 35000, Balance = 118000 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-5), EntryNumber = "J021", Description = "إيداع شيك", Debit = 22000, Credit = 0, Balance = 140000 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-2), EntryNumber = "J024", Description = "رسوم بنكية", Debit = 0, Credit = 1200, Balance = 138800 },
                    new LedgerEntry { Date = DateTime.Now.AddDays(-1), EntryNumber = "J026", Description = "تحويل من العميل: شركة المستقبل", Debit = 15000, Credit = 0, Balance = 153800 },
                    new LedgerEntry { Date = DateTime.Now, EntryNumber = "J029", Description = "سحب نقدي", Debit = 0, Credit = 33800, Balance = 120000 }
                };

                // إضافة حركات تجريبية لباقي الحسابات
                foreach (var accountNumber in _accountsInfo.Keys)
                {
                    if (!_accountEntries.ContainsKey(accountNumber))
                    {
                        _accountEntries[accountNumber] = new ObservableCollection<LedgerEntry>
                        {
                            new LedgerEntry { Date = DateTime.Now.AddDays(-30), EntryNumber = "J001", Description = "رصيد أول المدة", Debit = _accountsInfo[accountNumber].Balance, Credit = 0, Balance = _accountsInfo[accountNumber].Balance }
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateAccountInfo()
        {
            try
            {
                string selectedAccount = (cmbAccount.SelectedItem as ComboBoxItem)?.Content.ToString();
                if (string.IsNullOrEmpty(selectedAccount))
                    return;

                string accountNumber = selectedAccount.Split('-')[0].Trim();

                if (_accountsInfo.ContainsKey(accountNumber))
                {
                    var accountInfo = _accountsInfo[accountNumber];

                    txtAccountNumber.Text = accountInfo.AccountNumber;
                    txtAccountName.Text = accountInfo.AccountName;
                    txtAccountType.Text = accountInfo.AccountType;
                    txtAccountBalance.Text = accountInfo.Balance.ToString("N2") + " ر.س";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث معلومات الحساب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateLedgerEntries()
        {
            try
            {
                string selectedAccount = (cmbAccount.SelectedItem as ComboBoxItem)?.Content.ToString();
                if (string.IsNullOrEmpty(selectedAccount))
                    return;

                string accountNumber = selectedAccount.Split('-')[0].Trim();

                if (_accountEntries.ContainsKey(accountNumber))
                {
                    // فلترة الحركات حسب التاريخ
                    var filteredEntries = new ObservableCollection<LedgerEntry>();
                    double totalDebit = 0;
                    double totalCredit = 0;

                    foreach (var entry in _accountEntries[accountNumber])
                    {
                        if ((!dpFromDate.SelectedDate.HasValue || entry.Date >= dpFromDate.SelectedDate.Value) &&
                            (!dpToDate.SelectedDate.HasValue || entry.Date <= dpToDate.SelectedDate.Value))
                        {
                            filteredEntries.Add(entry);
                            totalDebit += entry.Debit;
                            totalCredit += entry.Credit;
                        }
                    }

                    dgLedgerEntries.ItemsSource = filteredEntries;

                    // تحديث الإحصائيات
                    txtTotalDebit.Text = totalDebit.ToString("N2") + " ر.س";
                    txtTotalCredit.Text = totalCredit.ToString("N2") + " ر.س";

                    // الرصيد النهائي هو آخر رصيد في الحركات المفلترة
                    if (filteredEntries.Count > 0)
                    {
                        txtFinalBalance.Text = filteredEntries[filteredEntries.Count - 1].Balance.ToString("N2") + " ر.س";
                    }
                    else
                    {
                        txtFinalBalance.Text = "0.00 ر.س";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث حركات الحساب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void cmbAccount_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateAccountInfo();
            UpdateLedgerEntries();
        }

        private void btnShow_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateLedgerEntries();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري طباعة كشف الحساب...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnExport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري تصدير كشف الحساب إلى Excel...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnBackToAccounts_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // العودة إلى صفحة الحسابات الرئيسية
                if (NavigationService != null)
                {
                    NavigationService.Navigate(new AccountsPage());
                }
                else
                {
                    MessageBox.Show("لا يمكن العودة إلى صفحة الحسابات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء العودة للصفحة الرئيسية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class AccountInfo
    {
        public string AccountNumber { get; set; }
        public string AccountName { get; set; }
        public string AccountType { get; set; }
        public double Balance { get; set; }
    }

    public class LedgerEntry
    {
        public DateTime Date { get; set; }
        public string EntryNumber { get; set; }
        public string Description { get; set; }
        public double Debit { get; set; }
        public double Credit { get; set; }
        public double Balance { get; set; }
    }
}
