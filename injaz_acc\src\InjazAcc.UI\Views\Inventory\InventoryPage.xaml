<Page x:Class="InjazAcc.UI.Views.Inventory.InventoryPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Inventory"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="900"
      Title="المخزون"
      FlowDirection="RightToLeft"
      FontFamily="Arial"
      TextElement.FontSize="14"
      TextElement.FontWeight="Regular"
      TextElement.Foreground="{DynamicResource MaterialDesignBody}"
      Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة -->
        <TextBlock Grid.Row="0" Text="إدارة المخزون" Style="{StaticResource PageTitle}" Margin="20,20,20,10"/>

        <!-- محتوى الصفحة -->
        <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}" Margin="20,0,20,20">
            <!-- تبويب المخزون الرئيسي -->
            <TabItem Header="المخزون الرئيسي">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- أدوات التحكم -->
                    <Grid Grid.Row="0" Margin="0,10,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- اختيار المخزن -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <TextBlock Text="المخزن:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <ComboBox x:Name="cmbWarehouses" Width="200" SelectionChanged="cmbWarehouses_SelectionChanged">
                                <ComboBoxItem Content="المخزن الرئيسي" IsSelected="True"/>
                                <ComboBoxItem Content="مخزن الفرع الأول"/>
                                <ComboBoxItem Content="مخزن الفرع الثاني"/>
                            </ComboBox>
                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="إضافة مخزن جديد" Click="btnAddWarehouse_Click" Margin="5,0,0,0">
                                <materialDesign:PackIcon Kind="PlusCircleOutline" Width="24" Height="24"/>
                            </Button>
                        </StackPanel>

                        <!-- البحث -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBox x:Name="txtSearch" Width="300" materialDesign:HintAssist.Hint="بحث عن صنف..." Margin="0,0,10,0"/>
                            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="بحث" Click="btnSearch_Click"/>
                        </StackPanel>

                        <!-- أزرار الإجراءات -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                            <Button Style="{StaticResource ActionButton}" Content="إضافة صنف" Margin="0,0,10,0" Click="btnAddItem_Click"/>
                            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="تصدير إلى Excel" Click="btnExportToExcel_Click"/>
                        </StackPanel>
                    </Grid>

                    <!-- جدول المخزون -->
                    <DataGrid Grid.Row="1" x:Name="dgInventory" AutoGenerateColumns="False" CanUserAddRows="False" Style="{StaticResource DataGridStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الرمز" Binding="{Binding Code}" Width="100"/>
                            <DataGridTextColumn Header="اسم الصنف" Binding="{Binding Name}" Width="*"/>
                            <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                            <DataGridTextColumn Header="الكمية المتاحة" Binding="{Binding AvailableQuantity, StringFormat=N2}" Width="120"/>
                            <DataGridTextColumn Header="سعر الشراء" Binding="{Binding PurchasePrice, StringFormat=N2}" Width="120"/>
                            <DataGridTextColumn Header="سعر البيع" Binding="{Binding SalePrice, StringFormat=N2}" Width="120"/>
                            <DataGridTextColumn Header="القيمة الإجمالية" Binding="{Binding TotalValue, StringFormat=N2}" Width="120"/>
                            <DataGridTemplateColumn Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="تعديل" Click="btnEditItem_Click">
                                                <materialDesign:PackIcon Kind="Pencil" Width="18" Height="18"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="حذف" Click="btnDeleteItem_Click">
                                                <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="تفاصيل" Click="btnItemDetails_Click">
                                                <materialDesign:PackIcon Kind="Information" Width="18" Height="18"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- تبويب جرد المخزون -->
            <TabItem Header="جرد المخزون">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- أدوات التحكم -->
                    <Grid Grid.Row="0" Margin="0,10,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- اختيار المخزن -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <TextBlock Text="المخزن:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <ComboBox x:Name="cmbInventoryWarehouses" Width="200">
                                <ComboBoxItem Content="المخزن الرئيسي" IsSelected="True"/>
                                <ComboBoxItem Content="مخزن الفرع الأول"/>
                                <ComboBoxItem Content="مخزن الفرع الثاني"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- معلومات الجرد -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="تاريخ الجرد:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <DatePicker x:Name="dpInventoryDate" Width="150"/>
                        </StackPanel>

                        <!-- أزرار الإجراءات -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="بدء جرد جديد" Margin="0,0,10,0" Click="btnStartNewInventory_Click"/>
                            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="طباعة تقرير الجرد" Click="btnPrintInventoryReport_Click"/>
                        </StackPanel>
                    </Grid>

                    <!-- جدول الجرد -->
                    <DataGrid Grid.Row="1" x:Name="dgInventoryCount" AutoGenerateColumns="False" CanUserAddRows="False" Style="{StaticResource DataGridStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الرمز" Binding="{Binding Code}" Width="100"/>
                            <DataGridTextColumn Header="اسم الصنف" Binding="{Binding Name}" Width="*"/>
                            <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                            <DataGridTextColumn Header="الكمية في النظام" Binding="{Binding SystemQuantity, StringFormat=N2}" Width="120"/>
                            <DataGridTextColumn Header="الكمية الفعلية" Binding="{Binding ActualQuantity, StringFormat=N2}" Width="120" IsReadOnly="False"/>
                            <DataGridTextColumn Header="الفرق" Binding="{Binding Difference, StringFormat=N2}" Width="120"/>
                            <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="150" IsReadOnly="False"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- أزرار الإجراءات -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                        <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="حفظ الجرد" Margin="0,0,10,0" Click="btnSaveInventory_Click"/>
                        <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="تسوية الفروقات" Click="btnAdjustDifferences_Click"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <!-- تبويب حركة المخزون -->
            <TabItem Header="حركة المخزون">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- أدوات التحكم -->
                    <Grid Grid.Row="0" Margin="0,10,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- اختيار المخزن -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="0,0,20,0">
                            <TextBlock Text="المخزن:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <ComboBox x:Name="cmbMovementWarehouses" Width="150">
                                <ComboBoxItem Content="المخزن الرئيسي" IsSelected="True"/>
                                <ComboBoxItem Content="مخزن الفرع الأول"/>
                                <ComboBoxItem Content="مخزن الفرع الثاني"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- اختيار الصنف -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="0,0,20,0">
                            <TextBlock Text="الصنف:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <ComboBox x:Name="cmbItems" Width="150">
                                <ComboBoxItem Content="جميع الأصناف" IsSelected="True"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- الفترة -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="0,0,10,0">
                            <TextBlock Text="من:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <DatePicker x:Name="dpFromDate" Width="120"/>
                        </StackPanel>

                        <StackPanel Grid.Column="3" Orientation="Horizontal">
                            <TextBlock Text="إلى:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <DatePicker x:Name="dpToDate" Width="120"/>
                        </StackPanel>

                        <!-- أزرار الإجراءات -->
                        <StackPanel Grid.Column="5" Orientation="Horizontal">
                            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="عرض الحركة" Click="btnShowMovement_Click"/>
                        </StackPanel>
                    </Grid>

                    <!-- جدول حركة المخزون -->
                    <DataGrid Grid.Row="1" x:Name="dgInventoryMovement" AutoGenerateColumns="False" CanUserAddRows="False" Style="{StaticResource DataGridStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=yyyy-MM-dd}" Width="120"/>
                            <DataGridTextColumn Header="رقم المستند" Binding="{Binding DocumentNumber}" Width="120"/>
                            <DataGridTextColumn Header="نوع الحركة" Binding="{Binding MovementType}" Width="120"/>
                            <DataGridTextColumn Header="الصنف" Binding="{Binding ItemName}" Width="*"/>
                            <DataGridTextColumn Header="الكمية الواردة" Binding="{Binding InQuantity, StringFormat=N2}" Width="120"/>
                            <DataGridTextColumn Header="الكمية الصادرة" Binding="{Binding OutQuantity, StringFormat=N2}" Width="120"/>
                            <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance, StringFormat=N2}" Width="120"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</Page>
