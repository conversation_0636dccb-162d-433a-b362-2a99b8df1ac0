<Window x:Class="InjazAcc.UI.Views.Inventory.InventoryItemWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Inventory"
        mc:Ignorable="d"
        Title="إضافة صنف جديد" Height="500" Width="500"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        TextElement.FontSize="14"
        TextElement.FontWeight="Regular"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- عنوان النافذة -->
        <TextBlock Grid.Row="0" x:Name="txtWindowTitle" Text="إضافة صنف جديد" Style="{StaticResource PageTitle}" HorizontalAlignment="Center" Margin="0,0,0,20"/>
        
        <!-- نموذج إدخال البيانات -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- رمز الصنف -->
            <TextBlock Grid.Row="0" Grid.Column="0" Text="رمز الصنف:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtCode" Margin="0,5"/>
            
            <!-- اسم الصنف -->
            <TextBlock Grid.Row="1" Grid.Column="0" Text="اسم الصنف:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtName" Margin="0,5"/>
            
            <!-- الوحدة -->
            <TextBlock Grid.Row="2" Grid.Column="0" Text="الوحدة:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <ComboBox Grid.Row="2" Grid.Column="1" x:Name="cmbUnit" Margin="0,5">
                <ComboBoxItem Content="قطعة"/>
                <ComboBoxItem Content="كيلو"/>
                <ComboBoxItem Content="متر"/>
                <ComboBoxItem Content="لتر"/>
                <ComboBoxItem Content="علبة"/>
                <ComboBoxItem Content="كرتون"/>
                <ComboBoxItem Content="طن"/>
                <ComboBoxItem Content="رول"/>
            </ComboBox>
            
            <!-- الكمية المتاحة -->
            <TextBlock Grid.Row="3" Grid.Column="0" Text="الكمية المتاحة:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBox Grid.Row="3" Grid.Column="1" x:Name="txtAvailableQuantity" Margin="0,5" TextChanged="txtAvailableQuantity_TextChanged"/>
            
            <!-- سعر الشراء -->
            <TextBlock Grid.Row="4" Grid.Column="0" Text="سعر الشراء:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBox Grid.Row="4" Grid.Column="1" x:Name="txtPurchasePrice" Margin="0,5" TextChanged="txtPurchasePrice_TextChanged"/>
            
            <!-- سعر البيع -->
            <TextBlock Grid.Row="5" Grid.Column="0" Text="سعر البيع:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBox Grid.Row="5" Grid.Column="1" x:Name="txtSalePrice" Margin="0,5"/>
            
            <!-- القيمة الإجمالية -->
            <TextBlock Grid.Row="6" Grid.Column="0" Text="القيمة الإجمالية:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBox Grid.Row="6" Grid.Column="1" x:Name="txtTotalValue" IsReadOnly="True" Margin="0,5"/>
            
            <!-- الحد الأدنى للمخزون -->
            <TextBlock Grid.Row="7" Grid.Column="0" Text="الحد الأدنى:" Margin="0,0,10,0" VerticalAlignment="Center"/>
            <TextBox Grid.Row="7" Grid.Column="1" x:Name="txtMinimumQuantity" Margin="0,5"/>
            
            <!-- ملاحظات -->
            <TextBlock Grid.Row="8" Grid.Column="0" Text="ملاحظات:" Margin="0,0,10,0" VerticalAlignment="Top"/>
            <TextBox Grid.Row="8" Grid.Column="1" x:Name="txtNotes" TextWrapping="Wrap" AcceptsReturn="True" Height="60" Margin="0,5"/>
        </Grid>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Style="{StaticResource ActionButton}" Content="حفظ" Margin="5,0" Click="btnSave_Click"/>
            <Button Style="{StaticResource ActionButton}" Content="إلغاء" Margin="5,0" Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
