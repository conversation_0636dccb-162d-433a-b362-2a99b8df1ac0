namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج عنصر في الفاتورة
    /// </summary>
    public class InvoiceItem
    {
        public int Id { get; set; }
        public int InvoiceId { get; set; }
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxPercentage { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        
        // العلاقات
        public virtual Invoice Invoice { get; set; }
        public virtual Product Product { get; set; }
    }
}
