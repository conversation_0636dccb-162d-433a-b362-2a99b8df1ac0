<Page x:Class="InjazAcc.UI.Views.Accounts.OpeningBalancesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Accounts"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="900"
      Title="أرصدة أول المدة"
      FlowDirection="RightToLeft"
      FontFamily="Arial"
      TextElement.FontSize="14"
      TextElement.FontWeight="Regular"
      TextElement.Foreground="{DynamicResource MaterialDesignBody}"
      Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة وزر الرجوع -->
        <Grid Grid.Row="0" Margin="20,20,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0" Style="{StaticResource MaterialDesignFlatButton}"
                    ToolTip="الرجوع للحسابات" Click="btnBackToAccounts_Click"
                    HorizontalAlignment="Left" Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ArrowRight" Width="24" Height="24" VerticalAlignment="Center"/>
                    <TextBlock Text="الرجوع للحسابات" VerticalAlignment="Center" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>

            <TextBlock Grid.Column="1" Text="أرصدة أول المدة" Style="{StaticResource PageTitle}"/>
        </Grid>

        <!-- أدوات البحث والإضافة -->
        <Grid Grid.Row="1" Margin="20,0,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- البحث -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBox x:Name="txtSearch" Width="300" materialDesign:HintAssist.Hint="بحث عن حساب..." Margin="0,0,10,0"/>
                <ComboBox x:Name="cmbAccountType" Width="150" SelectedIndex="0" Margin="0,0,10,0">
                    <ComboBoxItem Content="الكل"/>
                    <ComboBoxItem Content="الأصول"/>
                    <ComboBoxItem Content="الخصوم"/>
                    <ComboBoxItem Content="حقوق الملكية"/>
                    <ComboBoxItem Content="الإيرادات"/>
                    <ComboBoxItem Content="المصروفات"/>
                </ComboBox>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="بحث" Margin="0,0,10,0" Click="btnSearch_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="عرض الكل" Click="btnShowAll_Click"/>
            </StackPanel>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="حفظ التغييرات" Margin="0,0,10,0" Click="btnSaveChanges_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="ترحيل الأرصدة" Click="btnPostBalances_Click"/>
            </StackPanel>
        </Grid>

        <!-- جدول أرصدة أول المدة -->
        <DataGrid Grid.Row="2" x:Name="dgOpeningBalances" AutoGenerateColumns="False" CanUserAddRows="False"
                  Style="{StaticResource DataGridStyle}" Margin="20,0,20,10">
            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم الحساب" Binding="{Binding AccountNumber}" Width="120" IsReadOnly="True"/>
                <DataGridTextColumn Header="اسم الحساب" Binding="{Binding AccountName}" Width="*" IsReadOnly="True"/>
                <DataGridTextColumn Header="نوع الحساب" Binding="{Binding AccountType}" Width="120" IsReadOnly="True"/>
                <DataGridTemplateColumn Header="رصيد مدين" Width="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding DebitBalance, StringFormat=N2}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <TextBox Text="{Binding DebitBalance, StringFormat=N2}" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextChanged="DebitBalance_TextChanged"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Header="رصيد دائن" Width="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding CreditBalance, StringFormat=N2}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                    <DataGridTemplateColumn.CellEditingTemplate>
                        <DataTemplate>
                            <TextBox Text="{Binding CreditBalance, StringFormat=N2}" HorizontalAlignment="Stretch" VerticalAlignment="Center" TextChanged="CreditBalance_TextChanged"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellEditingTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- شريط الإحصائيات -->
        <Grid Grid.Row="3" Margin="20,0,20,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Border Grid.Column="0" Style="{StaticResource StatCard}" Background="#4CAF50" Margin="0,0,5,0">
                <StackPanel>
                    <TextBlock Text="إجمالي الأرصدة المدينة" Style="{StaticResource StatTitle}"/>
                    <TextBlock x:Name="txtTotalDebit" Text="0.00 ر.س" Style="{StaticResource StatValue}"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="1" Style="{StaticResource StatCard}" Background="#2196F3" Margin="5,0">
                <StackPanel>
                    <TextBlock Text="إجمالي الأرصدة الدائنة" Style="{StaticResource StatTitle}"/>
                    <TextBlock x:Name="txtTotalCredit" Text="0.00 ر.س" Style="{StaticResource StatValue}"/>
                </StackPanel>
            </Border>

            <Border Grid.Column="2" Style="{StaticResource StatCard}" Background="#FF9800" Margin="5,0,0,0">
                <StackPanel>
                    <TextBlock Text="الفرق" Style="{StaticResource StatTitle}"/>
                    <TextBlock x:Name="txtDifference" Text="0.00 ر.س" Style="{StaticResource StatValue}"/>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Page>
