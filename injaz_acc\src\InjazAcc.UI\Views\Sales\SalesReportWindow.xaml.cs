using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Sales
{
    /// <summary>
    /// Interaction logic for SalesReportWindow.xaml
    /// </summary>
    public partial class SalesReportWindow : Window
    {
        private ObservableCollection<SalesReportItem> _reportItems;

        public SalesReportWindow()
        {
            InitializeComponent();

            // تعيين التواريخ الافتراضية
            dpFromDate.SelectedDate = DateTime.Now.AddDays(-30);
            dpToDate.SelectedDate = DateTime.Now;

            // تحميل بيانات تجريبية
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية للتقرير
                _reportItems = new ObservableCollection<SalesReportItem>
                {
                    new SalesReportItem { Date = DateTime.Now.AddDays(-1), InvoiceNumber = "INV-001", CustomerName = "شركة الأمل التجارية", TotalSales = 5250.50m, TotalDiscounts = 0, TotalTaxes = 787.58m, NetSales = 6038.08m },
                    new SalesReportItem { Date = DateTime.Now.AddDays(-3), InvoiceNumber = "INV-002", CustomerName = "مؤسسة النور", TotalSales = 3750.75m, TotalDiscounts = 200.00m, TotalTaxes = 532.61m, NetSales = 4083.36m },
                    new SalesReportItem { Date = DateTime.Now.AddDays(-5), InvoiceNumber = "INV-003", CustomerName = "شركة الإعمار", TotalSales = 8500.00m, TotalDiscounts = 500.00m, TotalTaxes = 1200.00m, NetSales = 9200.00m },
                    new SalesReportItem { Date = DateTime.Now.AddDays(-7), InvoiceNumber = "INV-004", CustomerName = "مؤسسة الفجر", TotalSales = 4200.25m, TotalDiscounts = 0, TotalTaxes = 630.04m, NetSales = 4830.29m },
                    new SalesReportItem { Date = DateTime.Now.AddDays(-10), InvoiceNumber = "INV-005", CustomerName = "شركة البناء الحديث", TotalSales = 3300.00m, TotalDiscounts = 150.00m, TotalTaxes = 472.50m, NetSales = 3622.50m }
                };

                dgSalesReport.ItemsSource = _reportItems;

                // حساب الإجمالي
                decimal totalSales = 0;
                foreach (var item in _reportItems)
                {
                    totalSales += item.NetSales;
                }

                txtTotalSales.Text = $"{totalSales:N2} ر.س";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnShowReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة التواريخ
                if (dpFromDate.SelectedDate == null || dpToDate.SelectedDate == null)
                {
                    MessageBox.Show("الرجاء تحديد فترة التقرير", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (dpFromDate.SelectedDate > dpToDate.SelectedDate)
                {
                    MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // عرض التقرير حسب النوع المحدد
                string reportType = (cmbReportType.SelectedItem as ComboBoxItem).Content.ToString();
                MessageBox.Show($"جاري عرض {reportType} للفترة من {dpFromDate.SelectedDate.Value.ToString("yyyy-MM-dd")} إلى {dpToDate.SelectedDate.Value.ToString("yyyy-MM-dd")}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);

                // إعادة تحميل البيانات التجريبية
                LoadSampleData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري طباعة التقرير...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnExportToExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري تصدير التقرير إلى Excel...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    public class SalesReportItem
    {
        public DateTime Date { get; set; }
        public string InvoiceNumber { get; set; }
        public string CustomerName { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalDiscounts { get; set; }
        public decimal TotalTaxes { get; set; }
        public decimal NetSales { get; set; }
    }
}
