<Page x:Class="InjazAcc.UI.Views.Reports.InventoryReportsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Reports"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="900"
      Title="تقارير المخزون"
      FlowDirection="RightToLeft"
      FontFamily="Arial"
      TextElement.FontSize="14"
      TextElement.FontWeight="Regular"
      TextElement.Foreground="{DynamicResource MaterialDesignBody}"
      Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة وزر الرجوع -->
        <Grid Grid.Row="0" Margin="20,20,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0" Style="{StaticResource MaterialDesignFlatButton}"
                    ToolTip="الرجوع لصفحة التقارير" Click="btnBackToReports_Click"
                    HorizontalAlignment="Left" Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ArrowRight" Width="24" Height="24" VerticalAlignment="Center"/>
                    <TextBlock Text="الرجوع للتقارير" VerticalAlignment="Center" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>

            <TextBlock Grid.Column="1" Text="تقارير المخزون" Style="{StaticResource PageTitle}"/>
        </Grid>

        <!-- أدوات التحكم -->
        <Grid Grid.Row="1" Margin="20,0,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- نوع التقرير -->
            <TextBlock Grid.Column="0" Text="نوع التقرير:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <ComboBox Grid.Column="1" x:Name="cmbReportType" Width="200" SelectedIndex="0" Margin="0,0,20,0" SelectionChanged="cmbReportType_SelectionChanged">
                <ComboBoxItem Content="تقرير أرصدة المخزون"/>
                <ComboBoxItem Content="تقرير حركة الأصناف"/>
                <ComboBoxItem Content="تقرير الأصناف الراكدة"/>
                <ComboBoxItem Content="تقرير الأصناف منخفضة الكمية"/>
                <ComboBoxItem Content="تقرير جرد المخزون"/>
                <ComboBoxItem Content="تقرير تقييم المخزون"/>
            </ComboBox>

            <!-- المستودع -->
            <TextBlock Grid.Column="2" Text="المستودع:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <ComboBox Grid.Column="3" x:Name="cmbWarehouse" Width="150" SelectedIndex="0" Margin="0,0,20,0">
                <ComboBoxItem Content="الكل"/>
                <ComboBoxItem Content="المستودع الرئيسي"/>
                <ComboBoxItem Content="مستودع الفرع 1"/>
                <ComboBoxItem Content="مستودع الفرع 2"/>
            </ComboBox>

            <!-- التاريخ -->
            <TextBlock Grid.Column="4" Text="التاريخ:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <DatePicker Grid.Column="5" x:Name="dpDate" Width="120" Margin="0,0,10,0"/>

            <!-- زر عرض التقرير -->
            <Button Grid.Column="6" Style="{StaticResource MaterialDesignRaisedButton}" Content="عرض التقرير" Click="btnShowReport_Click"/>
        </Grid>

        <!-- محتوى التقرير -->
        <Grid Grid.Row="2" Margin="20,0,20,20">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- جدول التقرير -->
            <DataGrid Grid.Row="0" x:Name="dgReport" AutoGenerateColumns="False" CanUserAddRows="False"
                      Style="{StaticResource DataGridStyle}" Margin="0,0,0,10">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="كود الصنف" Binding="{Binding ItemCode}" Width="100"/>
                    <DataGridTextColumn Header="اسم الصنف" Binding="{Binding ItemName}" Width="*"/>
                    <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                    <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity, StringFormat=N2}" Width="100"/>
                    <DataGridTextColumn Header="سعر التكلفة" Binding="{Binding CostPrice, StringFormat=N2}" Width="100"/>
                    <DataGridTextColumn Header="سعر البيع" Binding="{Binding SellPrice, StringFormat=N2}" Width="100"/>
                    <DataGridTextColumn Header="القيمة" Binding="{Binding Value, StringFormat=N2}" Width="120"/>
                    <DataGridTextColumn Header="المستودع" Binding="{Binding Warehouse}" Width="120"/>
                </DataGrid.Columns>
            </DataGrid>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Left">
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="طباعة التقرير" Margin="0,0,10,0" Click="btnPrintReport_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="تصدير Excel" Margin="0,0,10,0" Click="btnExportExcel_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="تصدير PDF" Click="btnExportPDF_Click"/>
            </StackPanel>

            <!-- الإجماليات -->
            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right">
                <TextBlock Text="عدد الأصناف:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="txtItemCount" Text="0" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>

                <TextBlock Text="إجمالي الكميات:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="txtTotalQuantity" Text="0.00" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>

                <TextBlock Text="إجمالي القيمة:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="txtTotalValue" Text="0.00" FontWeight="Bold" VerticalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
