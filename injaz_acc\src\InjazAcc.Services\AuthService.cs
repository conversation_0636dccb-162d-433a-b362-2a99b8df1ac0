using InjazAcc.Core.Interfaces;
using InjazAcc.Core.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace InjazAcc.Services
{
    /// <summary>
    /// تنفيذ خدمة المصادقة والتحقق من الصلاحيات
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly IUnitOfWork _unitOfWork;

        public AuthService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        // المصادقة
        public async Task<User> AuthenticateAsync(string username, string password)
        {
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                return null;

            var user = await _unitOfWork.Users.SingleOrDefaultAsync(u => u.Username == username);

            if (user == null)
                return null;

            if (!await VerifyPasswordAsync(user.PasswordHash, password))
                return null;

            // تحديث آخر تسجيل دخول
            user.LastLogin = DateTime.Now;
            _unitOfWork.Users.Update(user);
            await _unitOfWork.CompleteAsync();

            return user;
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);

            if (user == null)
                return false;

            if (!await VerifyPasswordAsync(user.PasswordHash, currentPassword))
                return false;

            user.PasswordHash = await GeneratePasswordHashAsync(newPassword);
            _unitOfWork.Users.Update(user);
            await _unitOfWork.CompleteAsync();

            return true;
        }

        public async Task<string> GeneratePasswordHashAsync(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return await Task.FromResult(BitConverter.ToString(hashedBytes).Replace("-", "").ToLower());
            }
        }

        public async Task<bool> VerifyPasswordAsync(string passwordHash, string password)
        {
            var computedHash = await GeneratePasswordHashAsync(password);
            return await Task.FromResult(computedHash == passwordHash);
        }

        // إدارة المستخدمين
        public async Task<User> GetUserByIdAsync(int id)
        {
            return await _unitOfWork.Users.GetByIdAsync(id);
        }

        public async Task<User> GetUserByUsernameAsync(string username)
        {
            return await _unitOfWork.Users.SingleOrDefaultAsync(u => u.Username == username);
        }

        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            return await _unitOfWork.Users.GetAllAsync();
        }

        public async Task<User> CreateUserAsync(User user, string password)
        {
            // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
            if (await _unitOfWork.Users.AnyAsync(u => u.Username == user.Username))
                throw new Exception("اسم المستخدم موجود بالفعل");

            user.PasswordHash = await GeneratePasswordHashAsync(password);
            user.CreatedAt = DateTime.Now;

            await _unitOfWork.Users.AddAsync(user);
            await _unitOfWork.CompleteAsync();

            return user;
        }

        public async Task UpdateUserAsync(User user)
        {
            // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم
            if (await _unitOfWork.Users.AnyAsync(u => u.Username == user.Username && u.Id != user.Id))
                throw new Exception("اسم المستخدم موجود بالفعل");

            _unitOfWork.Users.Update(user);
            await _unitOfWork.CompleteAsync();
        }

        public async Task DeleteUserAsync(int userId)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);

            if (user == null)
                throw new Exception("المستخدم غير موجود");

            _unitOfWork.Users.Remove(user);
            await _unitOfWork.CompleteAsync();
        }

        // إدارة الأدوار والصلاحيات
        public async Task<IEnumerable<Role>> GetUserRolesAsync(int userId)
        {
            var userRoles = await _unitOfWork.Users.Query()
                .Where(u => u.Id == userId)
                .SelectMany(u => u.UserRoles)
                .Select(ur => ur.Role)
                .ToListAsync();

            return userRoles;
        }

        public async Task<IEnumerable<Permission>> GetUserPermissionsAsync(int userId)
        {
            var permissions = await _unitOfWork.Users.Query()
                .Where(u => u.Id == userId)
                .SelectMany(u => u.UserRoles)
                .SelectMany(ur => ur.Role.RolePermissions)
                .Select(rp => rp.Permission)
                .Distinct()
                .ToListAsync();

            return permissions;
        }

        public async Task<bool> HasPermissionAsync(int userId, string permissionName)
        {
            var hasPermission = await _unitOfWork.Users.Query()
                .Where(u => u.Id == userId)
                .SelectMany(u => u.UserRoles)
                .SelectMany(ur => ur.Role.RolePermissions)
                .AnyAsync(rp => rp.Permission.Name == permissionName);

            return hasPermission;
        }

        public async Task AssignRoleToUserAsync(int userId, int roleId)
        {
            var userRole = new UserRole
            {
                UserId = userId,
                RoleId = roleId
            };

            // التحقق من عدم وجود الدور بالفعل للمستخدم
            var exists = await _unitOfWork.Users.Query()
                .Where(u => u.Id == userId)
                .SelectMany(u => u.UserRoles)
                .AnyAsync(ur => ur.RoleId == roleId);

            if (!exists)
            {
                await _unitOfWork.Users.AddAsync(new User { Id = userId, UserRoles = new List<UserRole> { userRole } });

                await _unitOfWork.CompleteAsync();
            }
        }

        public async Task RemoveRoleFromUserAsync(int userId, int roleId)
        {
            var userRole = await _unitOfWork.Users.Query()
                .Where(u => u.Id == userId)
                .SelectMany(u => u.UserRoles)
                .FirstOrDefaultAsync(ur => ur.RoleId == roleId);

            if (userRole != null)
            {
                _unitOfWork.Users.Remove(new User { Id = userId });

                await _unitOfWork.CompleteAsync();
            }
        }
    }
}
