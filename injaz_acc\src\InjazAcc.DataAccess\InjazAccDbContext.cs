using InjazAcc.Core.Models;
using Microsoft.EntityFrameworkCore;
using System;

namespace InjazAcc.DataAccess
{
    public class InjazAccDbContext : DbContext
    {
        public InjazAccDbContext(DbContextOptions<InjazAccDbContext> options) : base(options)
        {
        }

        public InjazAccDbContext()
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // استخدام SQLite كقاعدة بيانات افتراضية
                optionsBuilder.UseSqlite("Data Source=InjazAccDb.db");
            }
        }

        // المستخدمين والصلاحيات
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }

        // المنتجات والمخزون
        public DbSet<Product> Products { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<ProductWarehouse> ProductWarehouses { get; set; }
        public DbSet<InventoryTransfer> InventoryTransfers { get; set; }
        public DbSet<InventoryTransferItem> InventoryTransferItems { get; set; }

        // الفواتير والعملاء والموردين
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<InvoiceItem> InvoiceItems { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Payment> Payments { get; set; }

        // الحسابات المالية
        public DbSet<Account> Accounts { get; set; }
        public DbSet<JournalEntry> JournalEntries { get; set; }
        public DbSet<JournalEntryItem> JournalEntryItems { get; set; }
        // public DbSet<AccountingSettings> AccountingSettings { get; set; }

        // الشركاء والإعدادات
        public DbSet<Partner> Partners { get; set; }
        public DbSet<ProfitDistribution> ProfitDistributions { get; set; }
        public DbSet<ProfitDistributionItem> ProfitDistributionItems { get; set; }
        public DbSet<FiscalYear> FiscalYears { get; set; }
        public DbSet<CompanyInfo> CompanyInfo { get; set; }
        public DbSet<SystemSettings> SystemSettings { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين العلاقات والمفاتيح

            // UserRole (علاقة متعددة-متعددة بين المستخدمين والأدوار)
            modelBuilder.Entity<UserRole>()
                .HasKey(ur => new { ur.UserId, ur.RoleId });

            modelBuilder.Entity<UserRole>()
                .HasOne(ur => ur.User)
                .WithMany(u => u.UserRoles)
                .HasForeignKey(ur => ur.UserId);

            modelBuilder.Entity<UserRole>()
                .HasOne(ur => ur.Role)
                .WithMany(r => r.UserRoles)
                .HasForeignKey(ur => ur.RoleId);

            // RolePermission (علاقة متعددة-متعددة بين الأدوار والصلاحيات)
            modelBuilder.Entity<RolePermission>()
                .HasKey(rp => new { rp.RoleId, rp.PermissionId });

            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.Role)
                .WithMany(r => r.RolePermissions)
                .HasForeignKey(rp => rp.RoleId);

            modelBuilder.Entity<RolePermission>()
                .HasOne(rp => rp.Permission)
                .WithMany(p => p.RolePermissions)
                .HasForeignKey(rp => rp.PermissionId);

            // ProductWarehouse (علاقة متعددة-متعددة بين المنتجات والمخازن)
            modelBuilder.Entity<ProductWarehouse>()
                .HasKey(pw => new { pw.ProductId, pw.WarehouseId });

            modelBuilder.Entity<ProductWarehouse>()
                .HasOne(pw => pw.Product)
                .WithMany(p => p.ProductWarehouses)
                .HasForeignKey(pw => pw.ProductId);

            modelBuilder.Entity<ProductWarehouse>()
                .HasOne(pw => pw.Warehouse)
                .WithMany(w => w.ProductWarehouses)
                .HasForeignKey(pw => pw.WarehouseId);

            // InventoryTransfer (علاقة مع المخازن المصدر والوجهة)
            modelBuilder.Entity<InventoryTransfer>()
                .HasOne(it => it.SourceWarehouse)
                .WithMany(w => w.SourceTransfers)
                .HasForeignKey(it => it.SourceWarehouseId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<InventoryTransfer>()
                .HasOne(it => it.DestinationWarehouse)
                .WithMany(w => w.DestinationTransfers)
                .HasForeignKey(it => it.DestinationWarehouseId)
                .OnDelete(DeleteBehavior.Restrict);

            // Invoice (علاقة مع الفاتورة الأصلية في حالة المردودات)
            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.OriginalInvoice)
                .WithMany(i => i.ReturnInvoices)
                .HasForeignKey(i => i.OriginalInvoiceId)
                .OnDelete(DeleteBehavior.Restrict);

            // Category (علاقة ذاتية للفئات الفرعية)
            modelBuilder.Entity<Category>()
                .HasOne(c => c.ParentCategory)
                .WithMany(c => c.SubCategories)
                .HasForeignKey(c => c.ParentCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // Account (علاقة ذاتية للحسابات الفرعية)
            modelBuilder.Entity<Account>()
                .HasOne(a => a.ParentAccount)
                .WithMany(a => a.SubAccounts)
                .HasForeignKey(a => a.ParentAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            // تكوين AccountingSettings لتجنب مشاكل Cascade - معطل مؤقتاً
            /*
            modelBuilder.Entity<AccountingSettings>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(a => a.TreasuryAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<AccountingSettings>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(a => a.InventoryAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<AccountingSettings>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(a => a.SalesAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<AccountingSettings>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(a => a.CostOfSalesAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<AccountingSettings>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(a => a.CustomersAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<AccountingSettings>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(a => a.SuppliersAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<AccountingSettings>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(a => a.SalesReturnAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<AccountingSettings>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(a => a.PurchaseReturnAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<AccountingSettings>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(a => a.SalesTaxAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<AccountingSettings>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(a => a.PurchaseTaxAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<AccountingSettings>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(a => a.SalesDiscountAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<AccountingSettings>()
                .HasOne<Account>()
                .WithMany()
                .HasForeignKey(a => a.PurchaseDiscountAccountId)
                .OnDelete(DeleteBehavior.Restrict);
            */
        }

        public override int SaveChanges()
        {
            // يمكن إضافة منطق لتتبع التغييرات وتسجيل سجلات التدقيق هنا
            return base.SaveChanges();
        }
    }
}
