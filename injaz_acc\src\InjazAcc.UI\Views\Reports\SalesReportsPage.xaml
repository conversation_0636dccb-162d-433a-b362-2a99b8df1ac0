<Page x:Class="InjazAcc.UI.Views.Reports.SalesReportsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Reports"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="900"
      Title="تقارير المبيعات"
      FlowDirection="RightToLeft"
      FontFamily="Arial"
      TextElement.FontSize="14"
      TextElement.FontWeight="Regular"
      TextElement.Foreground="{DynamicResource MaterialDesignBody}"
      Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة وزر الرجوع -->
        <Grid Grid.Row="0" Margin="20,20,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0" Style="{StaticResource MaterialDesignFlatButton}"
                    ToolTip="الرجوع لصفحة التقارير" Click="btnBackToReports_Click"
                    HorizontalAlignment="Left" Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ArrowRight" Width="24" Height="24" VerticalAlignment="Center"/>
                    <TextBlock Text="الرجوع للتقارير" VerticalAlignment="Center" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>

            <TextBlock Grid.Column="1" Text="تقارير المبيعات" Style="{StaticResource PageTitle}"/>
        </Grid>

        <!-- أدوات التحكم -->
        <Grid Grid.Row="1" Margin="20,0,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- نوع التقرير -->
            <TextBlock Grid.Column="0" Text="نوع التقرير:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <ComboBox Grid.Column="1" x:Name="cmbReportType" Width="200" SelectedIndex="0" Margin="0,0,20,0" SelectionChanged="cmbReportType_SelectionChanged">
                <ComboBoxItem Content="تقرير المبيعات اليومي"/>
                <ComboBoxItem Content="تقرير المبيعات الشهري"/>
                <ComboBoxItem Content="تقرير المبيعات حسب العميل"/>
                <ComboBoxItem Content="تقرير المبيعات حسب الصنف"/>
                <ComboBoxItem Content="تقرير مرتجعات المبيعات"/>
                <ComboBoxItem Content="تقرير تحليل المبيعات"/>
            </ComboBox>

            <!-- الفترة -->
            <TextBlock Grid.Column="2" Text="من:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <DatePicker Grid.Column="3" x:Name="dpFromDate" Width="120" Margin="0,0,10,0"/>

            <TextBlock Grid.Column="4" Text="إلى:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <DatePicker Grid.Column="5" x:Name="dpToDate" Width="120" Margin="0,0,10,0"/>

            <!-- زر عرض التقرير -->
            <Button Grid.Column="6" Style="{StaticResource MaterialDesignRaisedButton}" Content="عرض التقرير" Click="btnShowReport_Click"/>
        </Grid>

        <!-- محتوى التقرير -->
        <Grid Grid.Row="2" Margin="20,0,20,20">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- جدول التقرير -->
            <DataGrid Grid.Row="0" x:Name="dgReport" AutoGenerateColumns="False" CanUserAddRows="False"
                      Style="{StaticResource DataGridStyle}" Margin="0,0,0,10">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="100"/>
                    <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=yyyy-MM-dd}" Width="100"/>
                    <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="150"/>
                    <DataGridTextColumn Header="إجمالي الفاتورة" Binding="{Binding Total, StringFormat=N2}" Width="120"/>
                    <DataGridTextColumn Header="الخصم" Binding="{Binding Discount, StringFormat=N2}" Width="100"/>
                    <DataGridTextColumn Header="الضريبة" Binding="{Binding Tax, StringFormat=N2}" Width="100"/>
                    <DataGridTextColumn Header="الصافي" Binding="{Binding Net, StringFormat=N2}" Width="120"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                </DataGrid.Columns>
            </DataGrid>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Left">
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="طباعة التقرير" Margin="0,0,10,0" Click="btnPrintReport_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="تصدير Excel" Margin="0,0,10,0" Click="btnExportExcel_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="تصدير PDF" Click="btnExportPDF_Click"/>
            </StackPanel>

            <!-- الإجماليات -->
            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right">
                <TextBlock Text="إجمالي المبيعات:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="txtTotalSales" Text="0.00" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>

                <TextBlock Text="إجمالي الخصومات:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="txtTotalDiscounts" Text="0.00" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>

                <TextBlock Text="إجمالي الضرائب:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="txtTotalTaxes" Text="0.00" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>

                <TextBlock Text="الصافي:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock x:Name="txtTotalNet" Text="0.00" FontWeight="Bold" VerticalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
