using System;
using System.Windows;

namespace InjazAcc.UI.Views.Purchases
{
    /// <summary>
    /// Interaction logic for QuickSupplierWindow.xaml
    /// </summary>
    public partial class QuickSupplierWindow : Window
    {
        private SupplierInfo _supplier;

        public SupplierInfo Supplier => _supplier;

        public QuickSupplierWindow()
        {
            InitializeComponent();
            _supplier = new SupplierInfo();
            InitializeControls();
        }

        private void InitializeControls()
        {
            // تعيين القيم الافتراضية
            txtCode.Text = GenerateNewCode();
        }

        private string GenerateNewCode()
        {
            // توليد رمز جديد (في التطبيق الحقيقي سيتم جلب الرمز من قاعدة البيانات)
            Random random = new Random();
            return $"S{random.Next(1000, 9999)}";
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم المورد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPhone.Text))
                {
                    MessageBox.Show("الرجاء إدخال رقم هاتف المورد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtPhone.Focus();
                    return;
                }

                // تحديث بيانات المورد
                _supplier.Code = txtCode.Text;
                _supplier.Name = txtName.Text;
                _supplier.Phone = txtPhone.Text;
                _supplier.Email = txtEmail.Text;
                _supplier.Address = txtAddress.Text;
                _supplier.Notes = txtNotes.Text;

                // إغلاق النافذة بنجاح
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إلغاء العملية وإغلاق النافذة
            this.DialogResult = false;
        }
    }

    /// <summary>
    /// نموذج بيانات المورد المبسط للاستخدام في واجهة المستخدم
    /// </summary>
    public class SupplierInfo
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string Notes { get; set; }
    }
}
