# دليل المستخدم - برنامج إنجاز المحاسبي

## كيفية تشغيل البرنامج

### الطريقة الأولى: من خلال Visual Studio
1. افتح مجلد المشروع في Visual Studio
2. اضغط F5 أو اختر "Start Debugging" من قائمة Debug

### الطريقة الثانية: من خلال سطر الأوامر
1. افتح Command Prompt أو PowerShell
2. انتقل إلى مجلد المشروع:
   ```
   cd "c:\Users\<USER>\Desktop\المشغل الوطني\injaz_acc\src\InjazAcc.UI"
   ```
3. شغل الأمر:
   ```
   dotnet run
   ```

## قاعدة البيانات

### موقع قاعدة البيانات
- قاعدة البيانات تُحفظ تلقائياً في مجلد المشروع
- اسم الملف: `InjazAccDb.db` (SQLite)
- المسار الكامل: `injaz_acc\src\InjazAcc.UI\InjazAccDb.db`

### النسخ الاحتياطي
- يُنصح بعمل نسخة احتياطية من ملف قاعدة البيانات بانتظام
- انسخ ملف `InjazAccDb.db` إلى مكان آمن

## استخدام البرنامج

### تسجيل الدخول
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### الصفحة الرئيسية (Dashboard)
- تعرض ملخص للمبيعات والمشتريات
- إحصائيات العملاء والموردين
- الأرصدة المالية

### إدارة العملاء
1. اذهب إلى قسم "العملاء"
2. لإضافة عميل جديد: اضغط "إضافة عميل"
3. لتعديل عميل: اضغط زر "تعديل" بجانب العميل
4. لحذف عميل: اضغط زر "حذف" بجانب العميل
5. البيانات تُحفظ تلقائياً في قاعدة البيانات

### إدارة الموردين
1. اذهب إلى قسم "الموردين"
2. لإضافة مورد جديد: اضغط "إضافة مورد"
3. لتعديل مورد: اضغط زر "تعديل" بجانب المورد
4. لحذف مورد: اضغط زر "حذف" بجانب المورد
5. البيانات تُحفظ تلقائياً في قاعدة البيانات

### إدارة المخزون
1. اذهب إلى قسم "المخزون"
2. لإضافة صنف جديد: اضغط "إضافة صنف"
3. لتعديل صنف: اضغط زر "تعديل" بجانب الصنف
4. لحذف صنف: اضغط زر "حذف" بجانب الصنف
5. البيانات تُحفظ تلقائياً في قاعدة البيانات

### المبيعات
1. اذهب إلى قسم "المبيعات"
2. لإنشاء فاتورة مبيعات جديدة: اضغط "فاتورة جديدة"
3. اختر العميل والأصناف
4. احفظ الفاتورة

### المشتريات
1. اذهب إلى قسم "المشتريات"
2. لإنشاء فاتورة مشتريات جديدة: اضغط "فاتورة جديدة"
3. اختر المورد والأصناف
4. احفظ الفاتورة

### التقارير
1. اذهب إلى قسم "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية
4. اضغط "عرض التقرير"

## حل المشاكل الشائعة

### البرنامج لا يحفظ البيانات
- تأكد من أن البرنامج يعمل بصلاحيات كافية
- تأكد من وجود مساحة كافية على القرص الصلب
- تأكد من عدم حذف ملف قاعدة البيانات

### رسالة خطأ عند بدء التشغيل
- تأكد من تثبيت .NET 8.0 Runtime
- تأكد من عدم وجود برامج مضادة للفيروسات تمنع التشغيل

### البيانات لا تظهر
- أعد تشغيل البرنامج
- تأكد من وجود ملف قاعدة البيانات في المكان الصحيح

## الدعم الفني
للحصول على المساعدة، يرجى التواصل مع فريق الدعم الفني.

## إصدار البرنامج
الإصدار الحالي: 1.0.0
تاريخ الإصدار: 2025-08-02
