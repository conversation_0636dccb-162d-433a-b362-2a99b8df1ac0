using System;

namespace InjazAcc.Core.Interfaces
{
    /// <summary>
    /// واجهة لخدمة معالجة الاستثناءات
    /// </summary>
    public interface IExceptionHandler
    {
        /// <summary>
        /// معالجة الاستثناء وعرض رسالة مناسبة للمستخدم
        /// </summary>
        /// <param name="exception">الاستثناء المراد معالجته</param>
        /// <param name="showMessageToUser">ما إذا كان يجب عرض رسالة للمستخدم</param>
        /// <returns>رسالة الخطأ المعروضة للمستخدم</returns>
        string HandleException(Exception exception, bool showMessageToUser = true);
        
        /// <summary>
        /// تسجيل الاستثناء في ملف السجل
        /// </summary>
        /// <param name="exception">الاستثناء المراد تسجيله</param>
        void LogException(Exception exception);
    }
}
