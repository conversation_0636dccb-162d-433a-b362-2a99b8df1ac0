<Page x:Class="InjazAcc.UI.Views.Accounts.ChartOfAccountsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Accounts"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="900"
      Title="دليل الحسابات"
      FlowDirection="RightToLeft"
      FontFamily="Arial"
      TextElement.FontSize="14"
      TextElement.FontWeight="Regular"
      TextElement.Foreground="{DynamicResource MaterialDesignBody}"
      Background="{DynamicResource MaterialDesignPaper}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة وزر الرجوع -->
        <Grid Grid.Row="0" Margin="20,20,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0" Style="{StaticResource MaterialDesignFlatButton}"
                    ToolTip="الرجوع للحسابات" Click="btnBackToAccounts_Click"
                    HorizontalAlignment="Left" Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ArrowRight" Width="24" Height="24" VerticalAlignment="Center"/>
                    <TextBlock Text="الرجوع للحسابات" VerticalAlignment="Center" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>

            <TextBlock Grid.Column="1" Text="دليل الحسابات" Style="{StaticResource PageTitle}"/>
        </Grid>

        <!-- أدوات البحث والإضافة -->
        <Grid Grid.Row="1" Margin="20,0,20,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- البحث -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBox x:Name="txtSearch" Width="300" materialDesign:HintAssist.Hint="بحث عن حساب..." Margin="0,0,10,0"/>
                <ComboBox x:Name="cmbAccountType" Width="150" SelectedIndex="0" Margin="0,0,10,0">
                    <ComboBoxItem Content="الكل"/>
                    <ComboBoxItem Content="الأصول"/>
                    <ComboBoxItem Content="الخصوم"/>
                    <ComboBoxItem Content="حقوق الملكية"/>
                    <ComboBoxItem Content="الإيرادات"/>
                    <ComboBoxItem Content="المصروفات"/>
                </ComboBox>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="بحث" Margin="0,0,10,0" Click="btnSearch_Click"/>
                <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="عرض الكل" Click="btnShowAll_Click"/>
            </StackPanel>

            <!-- إضافة حساب جديد -->
            <Button Grid.Column="1" Style="{StaticResource MaterialDesignRaisedButton}" Content="إضافة حساب جديد" Click="btnAddAccount_Click"/>
        </Grid>

        <!-- جدول الحسابات -->
        <DataGrid Grid.Row="2" x:Name="dgAccounts" AutoGenerateColumns="False" CanUserAddRows="False"
                  Style="{StaticResource DataGridStyle}" Margin="20,0,20,20">
            <DataGrid.Columns>
                <DataGridTextColumn Header="رقم الحساب" Binding="{Binding AccountNumber}" Width="100"/>
                <DataGridTextColumn Header="اسم الحساب" Binding="{Binding AccountName}" Width="*"/>
                <DataGridTextColumn Header="نوع الحساب" Binding="{Binding AccountType}" Width="120"/>
                <DataGridTextColumn Header="المستوى" Binding="{Binding Level}" Width="80"/>
                <DataGridTextColumn Header="الحساب الرئيسي" Binding="{Binding ParentAccount}" Width="150"/>
                <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance, StringFormat=N2}" Width="120"/>
                <DataGridTemplateColumn Width="120">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="تعديل" Click="btnEditAccount_Click">
                                    <materialDesign:PackIcon Kind="Pencil" Width="18" Height="18"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="حذف" Click="btnDeleteAccount_Click">
                                    <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="عرض الحركات" Click="btnViewLedger_Click">
                                    <materialDesign:PackIcon Kind="BookOpenPageVariant" Width="18" Height="18"/>
                                </Button>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</Page>
