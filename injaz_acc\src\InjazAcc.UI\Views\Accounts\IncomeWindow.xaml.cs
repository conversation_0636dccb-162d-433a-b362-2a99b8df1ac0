using System;
using System.Windows;
using Microsoft.Win32;

namespace InjazAcc.UI.Views.Accounts
{
    /// <summary>
    /// Interaction logic for IncomeWindow.xaml
    /// </summary>
    public partial class IncomeWindow : Window
    {
        public DateTime TransactionDate { get; private set; }
        public double Amount { get; private set; }
        public string Account { get; private set; }
        public string Description { get; private set; }
        public string Attachment { get; private set; }

        public IncomeWindow()
        {
            InitializeComponent();
            dpDate.SelectedDate = DateTime.Now;
            cmbAccount.SelectedIndex = 0;
        }

        private void btnBrowse_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Filter = "جميع الملفات (*.*)|*.*|ملفات PDF (*.pdf)|*.pdf|ملفات الصور (*.jpg, *.jpeg, *.png)|*.jpg;*.jpeg;*.png";
                
                if (openFileDialog.ShowDialog() == true)
                {
                    txtAttachment.Text = openFileDialog.FileName;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!dpDate.SelectedDate.HasValue)
                {
                    MessageBox.Show("الرجاء تحديد التاريخ", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    dpDate.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtAmount.Text))
                {
                    MessageBox.Show("الرجاء إدخال المبلغ", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtAmount.Focus();
                    return;
                }

                if (!double.TryParse(txtAmount.Text, out double amount) || amount <= 0)
                {
                    MessageBox.Show("الرجاء إدخال مبلغ صحيح أكبر من صفر", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtAmount.Focus();
                    return;
                }

                if (cmbAccount.SelectedItem == null)
                {
                    MessageBox.Show("الرجاء اختيار الحساب", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    cmbAccount.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtDescription.Text))
                {
                    MessageBox.Show("الرجاء إدخال البيان", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtDescription.Focus();
                    return;
                }

                // حفظ البيانات
                TransactionDate = dpDate.SelectedDate.Value;
                Amount = amount;
                Account = (cmbAccount.SelectedItem as System.Windows.Controls.ComboBoxItem)?.Content.ToString();
                Description = txtDescription.Text;
                Attachment = txtAttachment.Text;

                // إغلاق النافذة بنجاح
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إلغاء العملية وإغلاق النافذة
            this.DialogResult = false;
        }
    }
}
