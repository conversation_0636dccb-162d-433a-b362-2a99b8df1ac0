using System;
using System.Windows;
using System.Windows.Controls;
using InjazAcc.UI.Views.Sales;
using InjazAcc.UI.Views.Purchases;
using InjazAcc.UI.Views.Customers;
using InjazAcc.UI.Views.Reports;

namespace InjazAcc.UI.Views
{
    /// <summary>
    /// Interaction logic for DashboardPage.xaml
    /// </summary>
    public partial class DashboardPage : Page
    {
        public DashboardPage()
        {
            InitializeComponent();
        }

        /// <summary>
        /// معالج حدث طلب شراء للمنتجات منخفضة المخزون
        /// </summary>
        private void btnPurchaseRequest_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح نافذة طلب شراء جديد", "طلب شراء", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: إضافة نافذة طلب شراء
                // var purchaseRequestWindow = new PurchaseRequestWindow();
                // purchaseRequestWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج حدث إنشاء فاتورة مبيعات جديدة
        /// </summary>
        private void btnNewSaleInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الانتقال إلى صفحة المبيعات أو فتح نافذة فاتورة جديدة
                if (NavigationService != null)
                {
                    NavigationService.Navigate(new SimpleSalesPage());
                }
                else
                {
                    MessageBox.Show("لا يمكن الانتقال إلى صفحة المبيعات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج حدث إنشاء فاتورة مشتريات جديدة
        /// </summary>
        private void btnNewPurchaseInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الانتقال إلى صفحة المشتريات أو فتح نافذة فاتورة جديدة
                if (NavigationService != null)
                {
                    NavigationService.Navigate(new SimplePurchasesPage());
                }
                else
                {
                    MessageBox.Show("لا يمكن الانتقال إلى صفحة المشتريات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة المشتريات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج حدث إضافة عميل جديد
        /// </summary>
        private void btnNewCustomer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الانتقال إلى صفحة العملاء أو فتح نافذة عميل جديد
                if (NavigationService != null)
                {
                    NavigationService.Navigate(new CustomersPage());
                }
                else
                {
                    MessageBox.Show("لا يمكن الانتقال إلى صفحة العملاء", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج حدث عرض تقرير المبيعات
        /// </summary>
        private void btnSalesReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الانتقال إلى صفحة التقارير أو فتح تقرير المبيعات مباشرة
                if (NavigationService != null)
                {
                    NavigationService.Navigate(new SalesReportsPage());
                }
                else
                {
                    MessageBox.Show("لا يمكن الانتقال إلى صفحة تقارير المبيعات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح تقرير المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
