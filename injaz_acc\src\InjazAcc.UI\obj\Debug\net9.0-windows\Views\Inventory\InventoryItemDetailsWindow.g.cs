﻿#pragma checksum "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "848BB3F5C10E1CCF4BB5A41C6494518EFB0F5C41"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InjazAcc.UI.Views.Inventory;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InjazAcc.UI.Views.Inventory {
    
    
    /// <summary>
    /// InventoryItemDetailsWindow
    /// </summary>
    public partial class InventoryItemDetailsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 27 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtItemName;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtCode;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtName;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtUnit;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtAvailableQuantity;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtPurchasePrice;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSalePrice;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalValue;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtMinimumQuantity;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgItemMovement;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgItemWarehouses;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InjazAcc.UI;component/views/inventory/inventoryitemdetailswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.txtItemName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.txtCode = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.txtName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.txtUnit = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.txtAvailableQuantity = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.txtPurchasePrice = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.txtSalePrice = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.txtTotalValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.txtMinimumQuantity = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.dgItemMovement = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 11:
            this.dgItemWarehouses = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 12:
            
            #line 125 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnPrintBarcode_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 135 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnPrintReport_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 136 "..\..\..\..\..\Views\Inventory\InventoryItemDetailsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnClose_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

