using System;
using System.Windows;
using System.Windows.Input;

namespace InjazAcc.UI.Views.Shared
{
    /// <summary>
    /// نافذة إضافة/تعديل عميل
    /// </summary>
    public class CustomerWindow : Window
    {
        public CustomerWindow()
        {
            try
            {
                // تهيئة النافذة
                Title = "إضافة عميل جديد";
                Width = 600;
                Height = 500;
                WindowStartupLocation = WindowStartupLocation.CenterScreen;
                
                // إظهار رسالة مؤقتة
                MessageBox.Show("نافذة إضافة عميل جديد - قيد التطوير", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // إغلاق النافذة
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة نافذة العميل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
