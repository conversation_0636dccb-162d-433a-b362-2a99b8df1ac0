namespace InjazAcc.Core.Models.FinancialStatements
{
    /// <summary>
    /// نموذج بيانات عنصر في قائمة الدخل
    /// </summary>
    public class IncomeStatementEntry
    {
        /// <summary>
        /// البيان
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// المبلغ
        /// </summary>
        public decimal Amount { get; set; }
        
        /// <summary>
        /// نوع العنصر (إيرادات، مصروفات، إجمالي، إلخ)
        /// </summary>
        public IncomeStatementEntryType Type { get; set; }
        
        /// <summary>
        /// هل هو عنوان قسم
        /// </summary>
        public bool IsHeader { get; set; }
        
        /// <summary>
        /// هل هو إجمالي
        /// </summary>
        public bool IsTotal { get; set; }
        
        /// <summary>
        /// مستوى العنصر (للتنسيق)
        /// </summary>
        public int Level { get; set; }
        
        /// <summary>
        /// رقم الحساب (إذا كان مرتبط بحساب)
        /// </summary>
        public string AccountCode { get; set; }
    }
    
    /// <summary>
    /// أنواع عناصر قائمة الدخل
    /// </summary>
    public enum IncomeStatementEntryType
    {
        Revenue = 1,         // إيرادات
        CostOfSales = 2,     // تكلفة المبيعات
        GrossProfit = 3,     // مجمل الربح
        Expense = 4,         // مصروفات
        OperatingProfit = 5, // الربح التشغيلي
        OtherIncome = 6,     // إيرادات أخرى
        OtherExpense = 7,    // مصروفات أخرى
        NetProfit = 8        // صافي الربح
    }
}
