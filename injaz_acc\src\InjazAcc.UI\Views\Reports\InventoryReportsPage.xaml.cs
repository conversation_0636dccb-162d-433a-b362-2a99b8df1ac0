using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace InjazAcc.UI.Views.Reports
{
    /// <summary>
    /// Interaction logic for InventoryReportsPage.xaml
    /// </summary>
    public partial class InventoryReportsPage : Page
    {
        public InventoryReportsPage()
        {
            try
            {
                InitializeComponent();
                LoadDummyData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة صفحة تقارير المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadDummyData()
        {
            try
            {
                // This method loads sample data for demonstration
                var items = new List<InventoryReportItem>
                {
                    new InventoryReportItem
                    {
                        ItemCode = "ITM001",
                        ItemName = "جهاز كمبيوتر محمول",
                        Unit = "قطعة",
                        Quantity = 25,
                        CostPrice = 1800,
                        SellPrice = 2200,
                        Value = 45000,
                        Warehouse = "المستودع الرئيسي"
                    },
                    new InventoryReportItem
                    {
                        ItemCode = "ITM002",
                        ItemName = "طابعة ليزر",
                        Unit = "قطعة",
                        Quantity = 15,
                        CostPrice = 800,
                        SellPrice = 1100,
                        Value = 12000,
                        Warehouse = "المستودع الرئيسي"
                    },
                    new InventoryReportItem
                    {
                        ItemCode = "ITM003",
                        ItemName = "شاشة كمبيوتر",
                        Unit = "قطعة",
                        Quantity = 30,
                        CostPrice = 500,
                        SellPrice = 750,
                        Value = 15000,
                        Warehouse = "مستودع الفرع 1"
                    },
                    new InventoryReportItem
                    {
                        ItemCode = "ITM004",
                        ItemName = "لوحة مفاتيح",
                        Unit = "قطعة",
                        Quantity = 50,
                        CostPrice = 80,
                        SellPrice = 120,
                        Value = 4000,
                        Warehouse = "مستودع الفرع 2"
                    },
                    new InventoryReportItem
                    {
                        ItemCode = "ITM005",
                        ItemName = "ماوس لاسلكي",
                        Unit = "قطعة",
                        Quantity = 40,
                        CostPrice = 40,
                        SellPrice = 70,
                        Value = 1600,
                        Warehouse = "المستودع الرئيسي"
                    }
                };

                if (dgReport != null)
                {
                    dgReport.ItemsSource = items;
                    UpdateTotals(items);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateTotals(List<InventoryReportItem> items)
        {
            try
            {
                if (txtItemCount == null || txtTotalQuantity == null || txtTotalValue == null)
                {
                    return;
                }

                double totalQuantity = 0;
                double totalValue = 0;

                foreach (var item in items)
                {
                    totalQuantity += item.Quantity;
                    totalValue += item.Value;
                }

                txtItemCount.Text = items.Count.ToString();
                txtTotalQuantity.Text = totalQuantity.ToString("N2");
                txtTotalValue.Text = totalValue.ToString("N2");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث الإجماليات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void cmbReportType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن هنا إعادة تحميل البيانات بناء على نوع التقرير المختار
            LoadDummyData();
        }

        private void btnShowReport_Click(object sender, RoutedEventArgs e)
        {
            // هنا يمكن تحميل بيانات التقرير بناء على المعايير المحددة
            MessageBox.Show("تم تحديث التقرير بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            LoadDummyData();
        }

        private void btnPrintReport_Click(object sender, RoutedEventArgs e)
        {
            // منطق طباعة التقرير
            MessageBox.Show("جاري طباعة التقرير...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void btnExportExcel_Click(object sender, RoutedEventArgs e)
        {
            // منطق تصدير التقرير إلى Excel
            MessageBox.Show("تم تصدير التقرير إلى Excel بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void btnExportPDF_Click(object sender, RoutedEventArgs e)
        {
            // منطق تصدير التقرير إلى PDF
            MessageBox.Show("تم تصدير التقرير إلى PDF بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void btnBackToReports_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // العودة إلى صفحة التقارير الرئيسية
                if (NavigationService != null)
                {
                    NavigationService.Navigate(new ReportsPage());
                }
                else
                {
                    MessageBox.Show("لا يمكن العودة إلى صفحة التقارير", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء العودة لصفحة التقارير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class InventoryReportItem
    {
        public string ItemCode { get; set; }
        public string ItemName { get; set; }
        public string Unit { get; set; }
        public double Quantity { get; set; }
        public double CostPrice { get; set; }
        public double SellPrice { get; set; }
        public double Value { get; set; }
        public string Warehouse { get; set; }
    }
}