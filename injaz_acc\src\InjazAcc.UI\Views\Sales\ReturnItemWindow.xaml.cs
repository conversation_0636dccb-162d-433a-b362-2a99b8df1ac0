using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Sales
{
    /// <summary>
    /// Interaction logic for ReturnItemWindow.xaml
    /// </summary>
    public partial class ReturnItemWindow : Window
    {
        public ReturnItem Item { get; private set; }
        private List<ReturnItem> _invoiceItems;
        private bool _isEditMode = false;

        public ReturnItemWindow(List<ReturnItem> invoiceItems)
        {
            InitializeComponent();
            _invoiceItems = invoiceItems;
            Item = new ReturnItem();

            // تعبئة قائمة الأصناف
            foreach (var item in _invoiceItems)
            {
                cmbProduct.Items.Add(item.Name);
            }
        }

        public ReturnItemWindow(ReturnItem item)
        {
            InitializeComponent();

            // التحقق من أن العنصر ليس فارغاً
            if (item == null)
            {
                MessageBox.Show("خطأ: العنصر المحدد غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Item = new ReturnItem();
                _invoiceItems = new List<ReturnItem>();
                return;
            }

            Item = new ReturnItem();
            _isEditMode = true;

            // تهيئة قائمة العناصر بهذا العنصر الوحيد
            _invoiceItems = new List<ReturnItem> { item };

            // تعبئة بيانات الصنف
            txtTitle.Text = "تعديل صنف المردود";
            cmbProduct.Items.Add(item.Name);
            cmbProduct.SelectedIndex = 0;
            cmbProduct.IsEnabled = false;

            txtCode.Text = item.Code;
            txtUnit.Text = item.Unit;
            txtSoldQuantity.Text = item.SoldQuantity.ToString();
            txtReturnQuantity.Text = item.ReturnQuantity.ToString();
            txtPrice.Text = item.Price.ToString("N2");
            txtTotal.Text = item.Total.ToString("N2");

            // نسخ خصائص العنصر الأصلي
            Item.Code = item.Code;
            Item.Name = item.Name;
            Item.Unit = item.Unit;
            Item.SoldQuantity = item.SoldQuantity;
            Item.ReturnQuantity = item.ReturnQuantity;
            Item.Price = item.Price;
            Item.Total = item.Total;
        }

        private void cmbProduct_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (cmbProduct.SelectedItem != null)
                {
                    string productName = cmbProduct.SelectedItem.ToString();

                    // التحقق من أن _invoiceItems ليست فارغة
                    if (_invoiceItems != null && _invoiceItems.Any())
                    {
                        var selectedItem = _invoiceItems.FirstOrDefault(i => i.Name == productName);

                        if (selectedItem != null)
                        {
                            // تعبئة بيانات الصنف المحدد
                            txtCode.Text = selectedItem.Code;
                            txtUnit.Text = selectedItem.Unit;
                            txtSoldQuantity.Text = selectedItem.SoldQuantity.ToString();
                            txtPrice.Text = selectedItem.Price.ToString("N2");

                            // تعيين كمية المردود الافتراضية
                            txtReturnQuantity.Text = "1";
                            CalculateTotal();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديد الصنف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void txtReturnQuantity_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotal();
        }

        private void CalculateTotal()
        {
            try
            {
                // التحقق من صحة المدخلات
                if (string.IsNullOrEmpty(txtReturnQuantity.Text) || string.IsNullOrEmpty(txtPrice.Text))
                {
                    return;
                }

                // تحويل المدخلات إلى أرقام
                int returnQuantity = int.Parse(txtReturnQuantity.Text);
                decimal price = decimal.Parse(txtPrice.Text);

                // حساب الإجمالي
                decimal total = returnQuantity * price;

                // عرض النتيجة
                txtTotal.Text = total.ToString("N2");
            }
            catch (Exception)
            {
                // تجاهل أخطاء التحويل
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من صحة البيانات
            if (cmbProduct.SelectedItem == null)
            {
                MessageBox.Show("الرجاء اختيار الصنف", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            if (string.IsNullOrEmpty(txtReturnQuantity.Text) || !int.TryParse(txtReturnQuantity.Text, out int returnQuantity) || returnQuantity <= 0)
            {
                MessageBox.Show("الرجاء إدخال كمية مردود صحيحة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // التحقق من أن كمية المردود لا تتجاوز الكمية المباعة
            int soldQuantity = int.Parse(txtSoldQuantity.Text);
            if (returnQuantity > soldQuantity)
            {
                MessageBox.Show("كمية المردود لا يمكن أن تتجاوز الكمية المباعة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // تعبئة بيانات الصنف
            Item.Code = txtCode.Text;
            Item.Name = cmbProduct.SelectedItem.ToString();
            Item.Unit = txtUnit.Text;
            Item.SoldQuantity = soldQuantity;
            Item.ReturnQuantity = returnQuantity;
            Item.Price = decimal.Parse(txtPrice.Text);
            Item.Total = decimal.Parse(txtTotal.Text);

            // إغلاق النافذة
            this.DialogResult = true;
            this.Close();
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إغلاق النافذة
            this.DialogResult = false;
            this.Close();
        }
    }
}
