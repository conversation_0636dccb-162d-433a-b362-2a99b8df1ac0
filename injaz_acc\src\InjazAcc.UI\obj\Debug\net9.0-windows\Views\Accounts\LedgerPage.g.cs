﻿#pragma checksum "..\..\..\..\..\Views\Accounts\LedgerPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1243E87B3FFA3A599F9EC3E12819221E674A9E0D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InjazAcc.UI.Views.Accounts;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InjazAcc.UI.Views.Accounts {
    
    
    /// <summary>
    /// LedgerPage
    /// </summary>
    public partial class LedgerPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 59 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbAccount;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpFromDate;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpToDate;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtAccountNumber;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtAccountName;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtAccountType;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtAccountBalance;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgLedgerEntries;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalDebit;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalCredit;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtFinalBalance;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InjazAcc.UI;component/views/accounts/ledgerpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 35 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnBackToAccounts_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.cmbAccount = ((System.Windows.Controls.ComboBox)(target));
            
            #line 59 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
            this.cmbAccount.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbAccount_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.dpFromDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.dpToDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            
            #line 88 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnShow_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 89 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnPrint_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 90 "..\..\..\..\..\Views\Accounts\LedgerPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnExport_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.txtAccountNumber = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.txtAccountName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.txtAccountType = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.txtAccountBalance = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.dgLedgerEntries = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 13:
            this.txtTotalDebit = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.txtTotalCredit = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.txtFinalBalance = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

