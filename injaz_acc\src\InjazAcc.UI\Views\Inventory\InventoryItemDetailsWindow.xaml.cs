using System;
using System.Collections.ObjectModel;
using System.Windows;

namespace InjazAcc.UI.Views.Inventory
{
    /// <summary>
    /// Interaction logic for InventoryItemDetailsWindow.xaml
    /// </summary>
    public partial class InventoryItemDetailsWindow : Window
    {
        private InventoryItem _item;
        private ObservableCollection<ItemMovement> _itemMovements;
        private ObservableCollection<ItemWarehouse> _itemWarehouses;

        public InventoryItemDetailsWindow(InventoryItem item)
        {
            InitializeComponent();
            _item = item;
            LoadItemData();
            LoadSampleData();
        }

        private void LoadItemData()
        {
            // تحميل بيانات الصنف
            if (_item != null)
            {
                txtItemName.Text = $"تفاصيل الصنف: {_item.Name}";
                txtCode.Text = _item.Code;
                txtName.Text = _item.Name;
                txtUnit.Text = _item.Unit;
                txtAvailableQuantity.Text = _item.AvailableQuantity.ToString("N2");
                txtPurchasePrice.Text = _item.PurchasePrice.ToString("N2") + " ر.س";
                txtSalePrice.Text = _item.SalePrice.ToString("N2") + " ر.س";
                txtTotalValue.Text = _item.TotalValue.ToString("N2") + " ر.س";
                txtMinimumQuantity.Text = "5"; // قيمة افتراضية للحد الأدنى
            }
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية لحركة الصنف
                _itemMovements = new ObservableCollection<ItemMovement>
                {
                    new ItemMovement { Date = DateTime.Now.AddDays(-1), DocumentNumber = "PUR-001", MovementType = "شراء", InQuantity = 5, OutQuantity = 0, Balance = 15 },
                    new ItemMovement { Date = DateTime.Now.AddDays(-2), DocumentNumber = "SAL-001", MovementType = "بيع", InQuantity = 0, OutQuantity = 2, Balance = 10 },
                    new ItemMovement { Date = DateTime.Now.AddDays(-5), DocumentNumber = "PUR-002", MovementType = "شراء", InQuantity = 10, OutQuantity = 0, Balance = 12 },
                    new ItemMovement { Date = DateTime.Now.AddDays(-7), DocumentNumber = "SAL-002", MovementType = "بيع", InQuantity = 0, OutQuantity = 3, Balance = 2 },
                    new ItemMovement { Date = DateTime.Now.AddDays(-10), DocumentNumber = "ADJ-001", MovementType = "تسوية", InQuantity = 5, OutQuantity = 0, Balance = 5 }
                };
                dgItemMovement.ItemsSource = _itemMovements;

                // بيانات تجريبية لتوزيع المخازن
                _itemWarehouses = new ObservableCollection<ItemWarehouse>
                {
                    new ItemWarehouse { WarehouseName = "المخزن الرئيسي", Quantity = 10, Value = 30000 },
                    new ItemWarehouse { WarehouseName = "مخزن الفرع الأول", Quantity = 3, Value = 9000 },
                    new ItemWarehouse { WarehouseName = "مخزن الفرع الثاني", Quantity = 2, Value = 6000 }
                };
                dgItemWarehouses.ItemsSource = _itemWarehouses;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintBarcode_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري طباعة الباركود...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري طباعة تقرير الصنف...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    public class ItemMovement
    {
        public DateTime Date { get; set; }
        public string DocumentNumber { get; set; }
        public string MovementType { get; set; }
        public double InQuantity { get; set; }
        public double OutQuantity { get; set; }
        public double Balance { get; set; }
    }

    public class ItemWarehouse
    {
        public string WarehouseName { get; set; }
        public double Quantity { get; set; }
        public double Value { get; set; }
    }
}
