using System;

namespace InjazAcc.Core.Exceptions
{
    /// <summary>
    /// استثناء مخصص لأخطاء قاعدة البيانات
    /// </summary>
    public class DatabaseException : InjazAccException
    {
        /// <summary>
        /// إنشاء استثناء جديد بدون رسالة
        /// </summary>
        public DatabaseException() : base("حدث خطأ أثناء الاتصال بقاعدة البيانات", "ERR-DB-001")
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        public DatabaseException(string message) : base(message, "ERR-DB-001")
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        public DatabaseException(string message, string errorCode) : base(message, errorCode)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public DatabaseException(string message, Exception innerException) : base(message, "ERR-DB-001", innerException)
        {
        }

        /// <summary>
        /// إنشاء استثناء جديد برسالة محددة ورمز خطأ واستثناء داخلي
        /// </summary>
        /// <param name="message">رسالة الخطأ</param>
        /// <param name="errorCode">رمز الخطأ</param>
        /// <param name="innerException">الاستثناء الداخلي</param>
        public DatabaseException(string message, string errorCode, Exception innerException) : base(message, errorCode, innerException)
        {
        }
    }
}
