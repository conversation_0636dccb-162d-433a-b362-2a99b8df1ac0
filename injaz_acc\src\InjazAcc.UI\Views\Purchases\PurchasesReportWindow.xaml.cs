using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Purchases
{
    /// <summary>
    /// Interaction logic for PurchasesReportWindow.xaml
    /// </summary>
    public partial class PurchasesReportWindow : Window
    {
        private ObservableCollection<PurchasesReportItem> _reportItems;

        public PurchasesReportWindow()
        {
            InitializeComponent();
            
            // تعيين التواريخ الافتراضية
            dpFromDate.SelectedDate = DateTime.Now.AddDays(-30);
            dpToDate.SelectedDate = DateTime.Now;
            
            // تحميل بيانات تجريبية
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية للتقرير
                _reportItems = new ObservableCollection<PurchasesReportItem>
                {
                    new PurchasesReportItem { Date = DateTime.Now.AddDays(-1), InvoiceNumber = "PUR-001", SupplierName = "شركة التوريدات العامة", TotalPurchases = 4250.50m, TotalDiscounts = 0, TotalTaxes = 637.58m, NetPurchases = 4888.08m },
                    new PurchasesReportItem { Date = DateTime.Now.AddDays(-3), InvoiceNumber = "PUR-002", SupplierName = "مؤسسة الإمداد", TotalPurchases = 2750.75m, TotalDiscounts = 150.00m, TotalTaxes = 390.11m, NetPurchases = 2990.86m },
                    new PurchasesReportItem { Date = DateTime.Now.AddDays(-5), InvoiceNumber = "PUR-003", SupplierName = "شركة المواد الأولية", TotalPurchases = 7500.00m, TotalDiscounts = 300.00m, TotalTaxes = 1080.00m, NetPurchases = 8280.00m },
                    new PurchasesReportItem { Date = DateTime.Now.AddDays(-7), InvoiceNumber = "PUR-004", SupplierName = "مؤسسة التجهيزات", TotalPurchases = 3200.25m, TotalDiscounts = 0, TotalTaxes = 480.04m, NetPurchases = 3680.29m },
                    new PurchasesReportItem { Date = DateTime.Now.AddDays(-10), InvoiceNumber = "PUR-005", SupplierName = "شركة المعدات الحديثة", TotalPurchases = 2300.00m, TotalDiscounts = 100.00m, TotalTaxes = 330.00m, NetPurchases = 2530.00m }
                };
                
                dgPurchasesReport.ItemsSource = _reportItems;
                
                // حساب الإجمالي
                decimal totalPurchases = 0;
                foreach (var item in _reportItems)
                {
                    totalPurchases += item.NetPurchases;
                }
                
                txtTotalPurchases.Text = $"{totalPurchases:N2} ر.س";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnShowReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة التواريخ
                if (dpFromDate.SelectedDate == null || dpToDate.SelectedDate == null)
                {
                    MessageBox.Show("الرجاء تحديد فترة التقرير", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (dpFromDate.SelectedDate > dpToDate.SelectedDate)
                {
                    MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // عرض التقرير حسب النوع المحدد
                string reportType = (cmbReportType.SelectedItem as ComboBoxItem).Content.ToString();
                MessageBox.Show($"جاري عرض {reportType} للفترة من {dpFromDate.SelectedDate.Value.ToString("yyyy-MM-dd")} إلى {dpToDate.SelectedDate.Value.ToString("yyyy-MM-dd")}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // إعادة تحميل البيانات التجريبية
                LoadSampleData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري طباعة التقرير...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnExportToExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري تصدير التقرير إلى Excel...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    public class PurchasesReportItem
    {
        public DateTime Date { get; set; }
        public string InvoiceNumber { get; set; }
        public string SupplierName { get; set; }
        public decimal TotalPurchases { get; set; }
        public decimal TotalDiscounts { get; set; }
        public decimal TotalTaxes { get; set; }
        public decimal NetPurchases { get; set; }
    }
}
