using System;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج سجل التدقيق في النظام
    /// </summary>
    public class AuditLog
    {
        public int Id { get; set; }
        public DateTime Timestamp { get; set; }
        public string EntityName { get; set; }
        public string EntityId { get; set; }
        public AuditAction Action { get; set; }
        public string OldValues { get; set; }
        public string NewValues { get; set; }
        public string Changes { get; set; }
        
        // العلاقة مع المستخدم الذي قام بالعملية
        public int? UserId { get; set; }
        public virtual User User { get; set; }
        
        public string IpAddress { get; set; }
    }
    
    /// <summary>
    /// أنواع عمليات التدقيق
    /// </summary>
    public enum AuditAction
    {
        Create = 1,      // إنشاء
        Update = 2,      // تحديث
        Delete = 3,      // حذف
        Login = 4,       // تسجيل دخول
        Logout = 5,      // تسجيل خروج
        View = 6,        // عرض
        Export = 7,      // تصدير
        Import = 8,      // استيراد
        Print = 9,       // طباعة
        Other = 10       // أخرى
    }
}
