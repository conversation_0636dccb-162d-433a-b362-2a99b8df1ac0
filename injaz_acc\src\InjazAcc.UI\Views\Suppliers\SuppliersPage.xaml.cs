using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using InjazAcc.Core.Interfaces;
using System.Threading.Tasks;
using System.Linq;

namespace InjazAcc.UI.Views.Suppliers
{
    /// <summary>
    /// Interaction logic for SuppliersPage.xaml
    /// </summary>
    public partial class SuppliersPage : Page
    {
        private ObservableCollection<Supplier> _suppliers;

        public SuppliersPage()
        {
            InitializeComponent();
            LoadSuppliersFromDatabase();
            UpdateStatistics();
        }

        private async void LoadSuppliersFromDatabase()
        {
            try
            {
                var unitOfWork = App.GetService<IUnitOfWork>();
                if (unitOfWork != null)
                {
                    var dbSuppliers = await unitOfWork.Suppliers.GetAllAsync();
                    _suppliers = new ObservableCollection<Supplier>();

                    foreach (var supplier in dbSuppliers)
                    {
                        _suppliers.Add(new Supplier
                        {
                            Code = supplier.Code,
                            Name = supplier.Name,
                            Phone = supplier.Phone ?? "",
                            Email = supplier.Email ?? "",
                            Address = supplier.Address ?? "",
                            Balance = (double)supplier.OpeningBalance
                        });
                    }
                }
                else
                {
                    // بيانات تجريبية للموردين
                    _suppliers = new ObservableCollection<Supplier>
                    {
                        new Supplier { Code = "S001", Name = "شركة التوريدات العامة", Phone = "0555111222", Email = "<EMAIL>", Address = "الرياض - شارع العليا", Balance = 25000 },
                        new Supplier { Code = "S002", Name = "مؤسسة الإمداد", Phone = "0555333444", Email = "<EMAIL>", Address = "جدة - شارع فلسطين", Balance = 18000 },
                        new Supplier { Code = "S003", Name = "شركة المواد الأولية", Phone = "0555555666", Email = "<EMAIL>", Address = "الدمام - شارع الملك فهد", Balance = 32000 },
                        new Supplier { Code = "S004", Name = "مؤسسة التجهيزات", Phone = "0555777888", Email = "<EMAIL>", Address = "الرياض - شارع التخصصي", Balance = 15000 },
                        new Supplier { Code = "S005", Name = "شركة المعدات المكتبية", Phone = "0555999000", Email = "<EMAIL>", Address = "مكة - شارع الحج", Balance = 22000 }
                    };
                }

                dgSuppliers.ItemsSource = _suppliers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            try
            {
                // تحديث الإحصائيات
                txtTotalSuppliers.Text = _suppliers.Count.ToString();
                
                double totalPurchases = 0;
                double totalDue = 0;
                
                foreach (var supplier in _suppliers)
                {
                    totalPurchases += supplier.TotalPurchases;
                    totalDue += supplier.Balance;
                }
                
                txtTotalPurchases.Text = totalPurchases.ToString("N2") + " ر.س";
                txtTotalDue.Text = totalDue.ToString("N2") + " ر.س";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث الإحصائيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string searchText = txtSearch.Text.Trim();
                if (string.IsNullOrEmpty(searchText))
                {
                    dgSuppliers.ItemsSource = _suppliers;
                    return;
                }

                string searchBy = (cmbSearchBy.SelectedItem as ComboBoxItem)?.Content.ToString();
                var filteredSuppliers = new ObservableCollection<Supplier>();

                foreach (var supplier in _suppliers)
                {
                    bool isMatch = false;

                    switch (searchBy)
                    {
                        case "الاسم":
                            isMatch = supplier.Name.Contains(searchText);
                            break;
                        case "رقم الهاتف":
                            isMatch = supplier.Phone.Contains(searchText);
                            break;
                        case "البريد الإلكتروني":
                            isMatch = supplier.Email.Contains(searchText);
                            break;
                        case "العنوان":
                            isMatch = supplier.Address.Contains(searchText);
                            break;
                        default:
                            isMatch = supplier.Name.Contains(searchText) || supplier.Phone.Contains(searchText);
                            break;
                    }

                    if (isMatch)
                    {
                        filteredSuppliers.Add(supplier);
                    }
                }

                dgSuppliers.ItemsSource = filteredSuppliers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void btnAddSupplier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var supplierWindow = new SupplierWindow();
                if (supplierWindow.ShowDialog() == true)
                {
                    // حفظ في قاعدة البيانات
                    var unitOfWork = App.GetService<IUnitOfWork>();
                    if (unitOfWork != null)
                    {
                        var dbSupplier = new InjazAcc.Core.Models.Supplier
                        {
                            Code = supplierWindow.Supplier.Code ?? "",
                            Name = supplierWindow.Supplier.Name ?? "",
                            ContactPerson = "",
                            Phone = supplierWindow.Supplier.Phone ?? "",
                            Email = supplierWindow.Supplier.Email ?? "",
                            Address = supplierWindow.Supplier.Address ?? "",
                            TaxNumber = "",
                            OpeningBalance = (decimal)supplierWindow.Supplier.Balance,
                            OpeningBalanceDate = DateTime.Now,
                            IsActive = true,
                            Notes = "",
                            CreatedAt = DateTime.Now
                        };

                        await unitOfWork.Suppliers.AddAsync(dbSupplier);
                        await unitOfWork.CompleteAsync();
                    }

                    _suppliers.Add(supplierWindow.Supplier);
                    UpdateStatistics();
                    MessageBox.Show("تم إضافة المورد بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void btnEditSupplier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var supplier = button.DataContext as Supplier;

                if (supplier != null)
                {
                    var supplierWindow = new SupplierWindow(supplier);
                    if (supplierWindow.ShowDialog() == true)
                    {
                        // تحديث في قاعدة البيانات
                        var unitOfWork = App.GetService<IUnitOfWork>();
                        if (unitOfWork != null)
                        {
                            var dbSupplier = await unitOfWork.Suppliers.FirstOrDefaultAsync(s => s.Code == supplier.Code);
                            if (dbSupplier != null)
                            {
                                dbSupplier.Name = supplierWindow.Supplier.Name ?? "";
                                dbSupplier.Phone = supplierWindow.Supplier.Phone ?? "";
                                dbSupplier.Email = supplierWindow.Supplier.Email ?? "";
                                dbSupplier.Address = supplierWindow.Supplier.Address ?? "";
                                dbSupplier.OpeningBalance = (decimal)supplierWindow.Supplier.Balance;

                                unitOfWork.Suppliers.Update(dbSupplier);
                                await unitOfWork.CompleteAsync();
                            }
                        }

                        int index = _suppliers.IndexOf(supplier);
                        if (index >= 0)
                        {
                            _suppliers[index] = supplierWindow.Supplier;
                            dgSuppliers.Items.Refresh();
                            UpdateStatistics();
                            MessageBox.Show("تم تعديل المورد بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void btnDeleteSupplier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var supplier = button.DataContext as Supplier;

                if (supplier != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف المورد: {supplier.Name}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // حذف من قاعدة البيانات
                        var unitOfWork = App.GetService<IUnitOfWork>();
                        if (unitOfWork != null)
                        {
                            var dbSupplier = await unitOfWork.Suppliers.FirstOrDefaultAsync(s => s.Code == supplier.Code);
                            if (dbSupplier != null)
                            {
                                unitOfWork.Suppliers.Remove(dbSupplier);
                                await unitOfWork.CompleteAsync();
                            }
                        }

                        _suppliers.Remove(supplier);
                        UpdateStatistics();
                        MessageBox.Show("تم حذف المورد بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSupplierDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var supplier = button.DataContext as Supplier;
                
                if (supplier != null)
                {
                    var supplierDetailsWindow = new SupplierDetailsWindow(supplier);
                    supplierDetailsWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void dgSuppliers_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (dgSuppliers.SelectedItem is Supplier supplier)
                {
                    var supplierDetailsWindow = new SupplierDetailsWindow(supplier);
                    supplierDetailsWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class Supplier
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public double Balance { get; set; }
        public double TotalPurchases { get; set; } = 0; // قيمة افتراضية للمشتريات
    }
}
