using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Accounts
{
    /// <summary>
    /// Interaction logic for ChartOfAccountsPage.xaml
    /// </summary>
    public partial class ChartOfAccountsPage : Page
    {
        private ObservableCollection<AccountItem> _accounts;

        public ChartOfAccountsPage()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية للحسابات
                _accounts = new ObservableCollection<AccountItem>
                {
                    // الأصول
                    new AccountItem { AccountNumber = "1000", AccountName = "الأصول", AccountType = "الأصول", Level = 1, ParentAccount = "", Balance = 520000 },
                    new AccountItem { AccountNumber = "1010", AccountName = "الصندوق", AccountType = "الأصول", Level = 2, ParentAccount = "1000 - الأصول", Balance = 35000 },
                    new AccountItem { AccountNumber = "1020", AccountName = "البنك", AccountType = "الأصول", Level = 2, ParentAccount = "1000 - الأصول", Balance = 120000 },
                    new AccountItem { AccountNumber = "1030", AccountName = "المخزون", AccountType = "الأصول", Level = 2, ParentAccount = "1000 - الأصول", Balance = 180000 },
                    new AccountItem { AccountNumber = "1040", AccountName = "العملاء", AccountType = "الأصول", Level = 2, ParentAccount = "1000 - الأصول", Balance = 65000 },
                    new AccountItem { AccountNumber = "1050", AccountName = "أثاث ومعدات", AccountType = "الأصول", Level = 2, ParentAccount = "1000 - الأصول", Balance = 120000 },

                    // الخصوم
                    new AccountItem { AccountNumber = "2000", AccountName = "الخصوم", AccountType = "الخصوم", Level = 1, ParentAccount = "", Balance = 200000 },
                    new AccountItem { AccountNumber = "2010", AccountName = "الموردين", AccountType = "الخصوم", Level = 2, ParentAccount = "2000 - الخصوم", Balance = 75000 },
                    new AccountItem { AccountNumber = "2020", AccountName = "قروض قصيرة الأجل", AccountType = "الخصوم", Level = 2, ParentAccount = "2000 - الخصوم", Balance = 100000 },
                    new AccountItem { AccountNumber = "2030", AccountName = "مصروفات مستحقة", AccountType = "الخصوم", Level = 2, ParentAccount = "2000 - الخصوم", Balance = 25000 },

                    // حقوق الملكية
                    new AccountItem { AccountNumber = "3000", AccountName = "حقوق الملكية", AccountType = "حقوق الملكية", Level = 1, ParentAccount = "", Balance = 375000 },
                    new AccountItem { AccountNumber = "3010", AccountName = "رأس المال", AccountType = "حقوق الملكية", Level = 2, ParentAccount = "3000 - حقوق الملكية", Balance = 350000 },
                    new AccountItem { AccountNumber = "3020", AccountName = "الأرباح المحتجزة", AccountType = "حقوق الملكية", Level = 2, ParentAccount = "3000 - حقوق الملكية", Balance = 25000 },

                    // الإيرادات
                    new AccountItem { AccountNumber = "4000", AccountName = "الإيرادات", AccountType = "الإيرادات", Level = 1, ParentAccount = "", Balance = 265000 },
                    new AccountItem { AccountNumber = "4010", AccountName = "المبيعات", AccountType = "الإيرادات", Level = 2, ParentAccount = "4000 - الإيرادات", Balance = 250000 },
                    new AccountItem { AccountNumber = "4020", AccountName = "إيرادات أخرى", AccountType = "الإيرادات", Level = 2, ParentAccount = "4000 - الإيرادات", Balance = 15000 },

                    // المصروفات
                    new AccountItem { AccountNumber = "5000", AccountName = "المصروفات", AccountType = "المصروفات", Level = 1, ParentAccount = "", Balance = 320000 },
                    new AccountItem { AccountNumber = "5010", AccountName = "تكلفة المبيعات", AccountType = "المصروفات", Level = 2, ParentAccount = "5000 - المصروفات", Balance = 150000 },
                    new AccountItem { AccountNumber = "5020", AccountName = "رواتب وأجور", AccountType = "المصروفات", Level = 2, ParentAccount = "5000 - المصروفات", Balance = 85000 },
                    new AccountItem { AccountNumber = "5030", AccountName = "إيجارات", AccountType = "المصروفات", Level = 2, ParentAccount = "5000 - المصروفات", Balance = 35000 },
                    new AccountItem { AccountNumber = "5040", AccountName = "مصروفات عمومية وإدارية", AccountType = "المصروفات", Level = 2, ParentAccount = "5000 - المصروفات", Balance = 50000 }
                };

                dgAccounts.ItemsSource = _accounts;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string searchText = txtSearch.Text.Trim();
                string accountType = (cmbAccountType.SelectedItem as ComboBoxItem)?.Content.ToString();

                if (string.IsNullOrEmpty(searchText) && (accountType == "الكل" || accountType == null))
                {
                    dgAccounts.ItemsSource = _accounts;
                    return;
                }

                var filteredAccounts = new ObservableCollection<AccountItem>();

                foreach (var account in _accounts)
                {
                    bool typeMatch = accountType == "الكل" || accountType == null || account.AccountType == accountType;
                    bool textMatch = string.IsNullOrEmpty(searchText) ||
                                    account.AccountNumber.Contains(searchText) ||
                                    account.AccountName.Contains(searchText);

                    if (typeMatch && textMatch)
                    {
                        filteredAccounts.Add(account);
                    }
                }

                dgAccounts.ItemsSource = filteredAccounts;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnShowAll_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                txtSearch.Text = "";
                cmbAccountType.SelectedIndex = 0;
                dgAccounts.ItemsSource = _accounts;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnAddAccount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var accountWindow = new AccountWindow();
                if (accountWindow.ShowDialog() == true)
                {
                    // إضافة الحساب الجديد إلى القائمة
                    _accounts.Add(new AccountItem
                    {
                        AccountNumber = accountWindow.AccountNumber,
                        AccountName = accountWindow.AccountName,
                        AccountType = accountWindow.AccountType,
                        Level = accountWindow.Level,
                        ParentAccount = accountWindow.ParentAccount,
                        Balance = 0
                    });

                    MessageBox.Show("تم إضافة الحساب بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditAccount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var account = button.DataContext as AccountItem;

                if (account != null)
                {
                    var accountWindow = new AccountWindow(account);
                    if (accountWindow.ShowDialog() == true)
                    {
                        // تحديث بيانات الحساب
                        account.AccountNumber = accountWindow.AccountNumber;
                        account.AccountName = accountWindow.AccountName;
                        account.AccountType = accountWindow.AccountType;
                        account.Level = accountWindow.Level;
                        account.ParentAccount = accountWindow.ParentAccount;

                        // تحديث العرض
                        dgAccounts.Items.Refresh();

                        MessageBox.Show("تم تعديل الحساب بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDeleteAccount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var account = button.DataContext as AccountItem;

                if (account != null)
                {
                    // التحقق من وجود حسابات فرعية
                    bool hasChildren = false;
                    foreach (var item in _accounts)
                    {
                        if (item.ParentAccount == $"{account.AccountNumber} - {account.AccountName}")
                        {
                            hasChildren = true;
                            break;
                        }
                    }

                    if (hasChildren)
                    {
                        MessageBox.Show("لا يمكن حذف هذا الحساب لأنه يحتوي على حسابات فرعية", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // التحقق من وجود رصيد
                    if (account.Balance != 0)
                    {
                        MessageBox.Show("لا يمكن حذف هذا الحساب لأنه يحتوي على رصيد", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // تأكيد الحذف
                    var result = MessageBox.Show($"هل أنت متأكد من حذف الحساب: {account.AccountName}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (result == MessageBoxResult.Yes)
                    {
                        _accounts.Remove(account);
                        MessageBox.Show("تم حذف الحساب بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnViewLedger_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var account = button.DataContext as AccountItem;

                if (account != null)
                {
                    // الانتقال إلى صفحة حساب الأستاذ مع تحديد الحساب المختار
                    NavigationService.Navigate(new LedgerPage());
                    MessageBox.Show($"تم الانتقال إلى حساب الأستاذ للحساب: {account.AccountName}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnBackToAccounts_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // العودة إلى صفحة الحسابات الرئيسية
                if (NavigationService != null)
                {
                    NavigationService.Navigate(new AccountsPage());
                }
                else
                {
                    MessageBox.Show("لا يمكن العودة إلى صفحة الحسابات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء العودة للصفحة الرئيسية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class AccountItem
    {
        public string AccountNumber { get; set; }
        public string AccountName { get; set; }
        public string AccountType { get; set; }
        public int Level { get; set; }
        public string ParentAccount { get; set; }
        public double Balance { get; set; }
    }
}
