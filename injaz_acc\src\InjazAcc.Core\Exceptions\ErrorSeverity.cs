namespace InjazAcc.Core.Exceptions
{
    /// <summary>
    /// تعداد يمثل مستويات خطورة الاستثناءات
    /// </summary>
    public enum ErrorSeverity
    {
        /// <summary>
        /// معلومات - لا تؤثر على سير العمل
        /// </summary>
        Information,

        /// <summary>
        /// تحذير - قد يؤثر على سير العمل ولكن يمكن الاستمرار
        /// </summary>
        Warning,

        /// <summary>
        /// خطأ - يؤثر على سير العمل ويتطلب معالجة
        /// </summary>
        Error,

        /// <summary>
        /// خطأ حرج - يمنع استمرار العمل ويتطلب معالجة فورية
        /// </summary>
        Critical,

        /// <summary>
        /// سؤال - يتطلب إجابة من المستخدم
        /// </summary>
        Question
    }
}
