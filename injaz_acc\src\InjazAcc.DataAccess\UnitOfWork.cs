using InjazAcc.Core.Interfaces;
using InjazAcc.Core.Models;
using InjazAcc.DataAccess.Repositories;
using System;
using System.Threading.Tasks;

namespace InjazAcc.DataAccess
{
    /// <summary>
    /// تنفيذ وحدة العمل للتعامل مع المستودعات
    /// </summary>
    public class UnitOfWork : IUnitOfWork
    {
        private readonly InjazAccDbContext _context;
        private bool _disposed = false;

        // المستودعات
        private IRepository<User> _users;
        private IRepository<Role> _roles;
        private IRepository<Permission> _permissions;
        private IRepository<Product> _products;
        private IRepository<Category> _categories;
        private IRepository<Unit> _units;
        private IRepository<Warehouse> _warehouses;
        private IRepository<Invoice> _invoices;
        private IRepository<InvoiceItem> _invoiceItems;
        private IRepository<Customer> _customers;
        private IRepository<Supplier> _suppliers;
        private IRepository<Payment> _payments;
        private IRepository<Account> _accounts;
        private IRepository<JournalEntry> _journalEntries;
        private IRepository<JournalEntryItem> _journalEntryItems;
        private IRepository<AccountingSettings> _accountingSettings;
        private IRepository<Partner> _partners;
        private IRepository<FiscalYear> _fiscalYears;
        private IRepository<CompanyInfo> _companyInfo;
        private IRepository<SystemSettings> _systemSettings;
        private IRepository<AuditLog> _auditLogs;

        public UnitOfWork(InjazAccDbContext context)
        {
            _context = context;
        }

        // المستودعات
        public IRepository<User> Users => _users ??= new Repository<User>(_context);
        public IRepository<Role> Roles => _roles ??= new Repository<Role>(_context);
        public IRepository<Permission> Permissions => _permissions ??= new Repository<Permission>(_context);
        public IRepository<Product> Products => _products ??= new Repository<Product>(_context);
        public IRepository<Category> Categories => _categories ??= new Repository<Category>(_context);
        public IRepository<Unit> Units => _units ??= new Repository<Unit>(_context);
        public IRepository<Warehouse> Warehouses => _warehouses ??= new Repository<Warehouse>(_context);
        public IRepository<Invoice> Invoices => _invoices ??= new Repository<Invoice>(_context);
        public IRepository<InvoiceItem> InvoiceItems => _invoiceItems ??= new Repository<InvoiceItem>(_context);
        public IRepository<Customer> Customers => _customers ??= new Repository<Customer>(_context);
        public IRepository<Supplier> Suppliers => _suppliers ??= new Repository<Supplier>(_context);
        public IRepository<Payment> Payments => _payments ??= new Repository<Payment>(_context);
        public IRepository<Account> Accounts => _accounts ??= new Repository<Account>(_context);
        public IRepository<JournalEntry> JournalEntries => _journalEntries ??= new Repository<JournalEntry>(_context);
        public IRepository<JournalEntryItem> JournalEntryItems => _journalEntryItems ??= new Repository<JournalEntryItem>(_context);
        public IRepository<AccountingSettings> AccountingSettings => _accountingSettings ??= new Repository<AccountingSettings>(_context);
        public IRepository<Partner> Partners => _partners ??= new Repository<Partner>(_context);
        public IRepository<FiscalYear> FiscalYears => _fiscalYears ??= new Repository<FiscalYear>(_context);
        public IRepository<CompanyInfo> CompanyInfo => _companyInfo ??= new Repository<CompanyInfo>(_context);
        public IRepository<SystemSettings> SystemSettings => _systemSettings ??= new Repository<SystemSettings>(_context);
        public IRepository<AuditLog> AuditLogs => _auditLogs ??= new Repository<AuditLog>(_context);

        // حفظ التغييرات
        public async Task<int> CompleteAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        // التخلص من الموارد
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _context.Dispose();
                }

                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
