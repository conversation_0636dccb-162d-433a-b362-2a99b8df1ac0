<Window x:Class="InjazAcc.UI.Views.Sales.QuickCustomerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Sales"
        mc:Ignorable="d"
        Title="إضافة عميل جديد" Height="400" Width="450"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        TextElement.FontSize="14"
        TextElement.FontWeight="Regular"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- عنوان النافذة -->
        <TextBlock Grid.Row="0" x:Name="txtWindowTitle" Text="إضافة عميل جديد" Style="{StaticResource PageTitle}" HorizontalAlignment="Center"/>
        
        <!-- نموذج البيانات -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="0,20,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- رمز العميل -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="الرمز:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtCode" Margin="0,5" IsReadOnly="True"/>
                
                <!-- اسم العميل -->
                <TextBlock Grid.Row="1" Grid.Column="0" Text="الاسم:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtName" Margin="0,5"/>
                
                <!-- رقم الهاتف -->
                <TextBlock Grid.Row="2" Grid.Column="0" Text="الهاتف:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtPhone" Margin="0,5"/>
                
                <!-- البريد الإلكتروني -->
                <TextBlock Grid.Row="3" Grid.Column="0" Text="البريد الإلكتروني:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBox Grid.Row="3" Grid.Column="1" x:Name="txtEmail" Margin="0,5"/>
                
                <!-- العنوان -->
                <TextBlock Grid.Row="4" Grid.Column="0" Text="العنوان:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBox Grid.Row="4" Grid.Column="1" x:Name="txtAddress" Margin="0,5"/>
                
                <!-- ملاحظات -->
                <TextBlock Grid.Row="5" Grid.Column="0" Text="ملاحظات:" Margin="0,0,10,0" VerticalAlignment="Top"/>
                <TextBox Grid.Row="5" Grid.Column="1" x:Name="txtNotes" TextWrapping="Wrap" AcceptsReturn="True" Height="80" Margin="0,5"/>
            </Grid>
        </ScrollViewer>
        
        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="حفظ" Margin="5,0" Click="btnSave_Click"/>
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="إلغاء" Margin="5,0" Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
