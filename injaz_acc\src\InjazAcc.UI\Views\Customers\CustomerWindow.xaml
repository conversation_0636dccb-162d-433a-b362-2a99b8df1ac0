<Window x:Class="InjazAcc.UI.Views.Customers.CustomerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:InjazAcc.UI.Views.Customers"
        mc:Ignorable="d"
        Title="إضافة عميل جديد" Height="550" Width="500"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        TextElement.FontSize="14"
        TextElement.FontWeight="Regular"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان النافذة -->
        <TextBlock Grid.Row="0" x:Name="txtWindowTitle" Text="إضافة عميل جديد" Style="{StaticResource PageTitle}" HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- نموذج إدخال البيانات -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- رمز العميل -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="رمز العميل:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtCode" Margin="0,5"/>

                <!-- اسم العميل -->
                <TextBlock Grid.Row="1" Grid.Column="0" Text="اسم العميل:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtName" Margin="0,5"/>

                <!-- نوع العميل -->
                <TextBlock Grid.Row="2" Grid.Column="0" Text="نوع العميل:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <ComboBox Grid.Row="2" Grid.Column="1" x:Name="cmbType" Margin="0,5">
                    <ComboBoxItem Content="شركة"/>
                    <ComboBoxItem Content="مؤسسة"/>
                    <ComboBoxItem Content="فرد"/>
                </ComboBox>

                <!-- رقم الهاتف -->
                <TextBlock Grid.Row="3" Grid.Column="0" Text="رقم الهاتف:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBox Grid.Row="3" Grid.Column="1" x:Name="txtPhone" Margin="0,5"/>

                <!-- البريد الإلكتروني -->
                <TextBlock Grid.Row="4" Grid.Column="0" Text="البريد الإلكتروني:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBox Grid.Row="4" Grid.Column="1" x:Name="txtEmail" Margin="0,5"/>

                <!-- العنوان -->
                <TextBlock Grid.Row="5" Grid.Column="0" Text="العنوان:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBox Grid.Row="5" Grid.Column="1" x:Name="txtAddress" Margin="0,5"/>

                <!-- الرصيد الافتتاحي -->
                <TextBlock Grid.Row="6" Grid.Column="0" Text="الرصيد الافتتاحي:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBox Grid.Row="6" Grid.Column="1" x:Name="txtBalance" Margin="0,5"/>

                <!-- حد الائتمان -->
                <TextBlock Grid.Row="7" Grid.Column="0" Text="حد الائتمان:" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBox Grid.Row="7" Grid.Column="1" x:Name="txtCreditLimit" Margin="0,5"/>

                <!-- فترة السداد (بالأيام) -->
                <TextBlock Grid.Row="8" Grid.Column="0" Text="فترة السداد (بالأيام):" Margin="0,0,10,0" VerticalAlignment="Center"/>
                <TextBox Grid.Row="8" Grid.Column="1" x:Name="txtPaymentPeriod" Margin="0,5"/>

                <!-- ملاحظات -->
                <TextBlock Grid.Row="9" Grid.Column="0" Text="ملاحظات:" Margin="0,0,10,0" VerticalAlignment="Top"/>
                <TextBox Grid.Row="9" Grid.Column="1" x:Name="txtNotes" TextWrapping="Wrap" AcceptsReturn="True" Height="80" Margin="0,5"/>
            </Grid>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="حفظ" Margin="5,0" Click="btnSave_Click"/>
            <Button Style="{StaticResource MaterialDesignRaisedButton}" Content="إلغاء" Margin="5,0" Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
