using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Globalization;
using InjazAcc.Core.Models;
using InjazAcc.Services.Printing;
using InjazAcc.UI.Views.Shared;
using InjazAcc.UI.Views.Shared.PrintTemplates;

namespace InjazAcc.UI.Views.Purchases
{
    /// <summary>
    /// Interaction logic for NewPurchaseInvoiceWindow.xaml
    /// </summary>
    public partial class NewPurchaseInvoiceWindow : Window
    {
        private ObservableCollection<NewPurchaseItem> _items;
        private double _totalAmount = 0;

        public NewPurchaseInvoiceWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية للمنتجات
                _items = new ObservableCollection<NewPurchaseItem>
                {
                    new NewPurchaseItem { Code = "P001", Name = "مواد خام", Unit = "كيلو", Quantity = 50, Price = 15, Discount = 0, Total = 750 },
                    new NewPurchaseItem { Code = "P002", Name = "أدوات مكتبية", Unit = "علبة", Quantity = 10, Price = 50, Discount = 50, Total = 450 }
                };
                dgItems.ItemsSource = _items;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnAddItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة إضافة صنف جديد
                var itemWindow = new PurchaseItemWindow();
                if (itemWindow.ShowDialog() == true)
                {
                    // إضافة الصنف الجديد إلى القائمة
                    var newItem = itemWindow.Item;
                    _items.Add(newItem);

                    // تحديث إجمالي الفاتورة
                    UpdateInvoiceTotal();

                    MessageBox.Show("تمت إضافة الصنف بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة الصنف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateInvoiceTotal()
        {
            try
            {
                // حساب إجمالي الفاتورة
                double subtotal = 0;
                double totalDiscount = 0;

                foreach (var item in _items)
                {
                    subtotal += item.Price * item.Quantity;
                    totalDiscount += item.Discount;
                }

                // حساب الضريبة (15%)
                double taxRate = 0.15;
                double taxAmount = (subtotal - totalDiscount) * taxRate;

                // حساب الإجمالي النهائي
                double total = subtotal - totalDiscount + taxAmount;
                _totalAmount = total; // تحديث المتغير العام للإجمالي

                // تحديث العناصر المرئية
                txtSubtotal.Text = subtotal.ToString("N2") + " ر.س";
                txtTotalDiscount.Text = totalDiscount.ToString("N2") + " ر.س";
                txtTax.Text = taxAmount.ToString("N2") + " ر.س";
                txtTotal.Text = total.ToString("N2") + " ر.س";

                // تحديث المبلغ المسدد بناءً على طريقة الدفع المختارة
                if (rbCash != null && rbCash.IsChecked == true)
                {
                    txtPaidAmount.Text = total.ToString("N2");
                }
                else if (rbCredit != null && rbCredit.IsChecked == true)
                {
                    txtPaidAmount.Text = "0.00";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث إجمالي الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعديل الصنف المحدد
                var button = sender as Button;
                var item = button.DataContext as NewPurchaseItem;

                if (item != null)
                {
                    // فتح نافذة تعديل الصنف
                    var itemWindow = new PurchaseItemWindow(item);
                    if (itemWindow.ShowDialog() == true)
                    {
                        // تحديث الصنف في القائمة
                        int index = _items.IndexOf(item);
                        if (index >= 0)
                        {
                            _items[index] = itemWindow.Item;

                            // تحديث عرض البيانات
                            dgItems.Items.Refresh();

                            // تحديث إجمالي الفاتورة
                            UpdateInvoiceTotal();

                            MessageBox.Show("تم تعديل الصنف بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تعديل الصنف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDeleteItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حذف الصنف المحدد
                var button = sender as Button;
                var item = button.DataContext as NewPurchaseItem;

                if (item != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف الصنف: {item.Name}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        _items.Remove(item);

                        // تحديث إجمالي الفاتورة
                        UpdateInvoiceTotal();

                        MessageBox.Show("تم حذف الصنف بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف الصنف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود منتجات في الفاتورة
                if (_items.Count == 0)
                {
                    MessageBox.Show("لا يمكن حفظ الفاتورة لأنها لا تحتوي على أي منتجات", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // الحصول على طريقة الدفع والمبلغ المسدد
                string paymentMethod = "نقدًا";
                double paidAmount = _totalAmount;
                double remainingAmount = 0;

                if (rbPartial.IsChecked == true)
                {
                    paymentMethod = "جزئي";
                    if (double.TryParse(txtPaidAmount.Text, NumberStyles.Any, CultureInfo.InvariantCulture, out paidAmount))
                    {
                        remainingAmount = _totalAmount - paidAmount;
                    }
                    else
                    {
                        MessageBox.Show("الرجاء إدخال مبلغ مسدد صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        txtPaidAmount.Focus();
                        return;
                    }
                }
                else if (rbCredit.IsChecked == true)
                {
                    paymentMethod = "آجل";
                    paidAmount = 0;
                    remainingAmount = _totalAmount;
                }

                // في التطبيق الحقيقي، سيتم حفظ الفاتورة في قاعدة البيانات
                // SaveInvoiceToDatabase(supplier, items, paymentMethod, paidAmount, remainingAmount);

                // ترحيل المبالغ إلى الحسابات المناسبة
                if (paidAmount > 0)
                {
                    // ترحيل المبلغ المسدد إلى حساب الخزينة
                    // AddToTreasury(paidAmount, "مشتريات - فاتورة رقم " + invoiceNumber);
                    Console.WriteLine($"تم ترحيل مبلغ {paidAmount:N2} ر.س إلى حساب الخزينة");
                }

                if (remainingAmount > 0)
                {
                    // ترحيل المبلغ المتبقي إلى حساب المورد
                    string supplierName = ((ComboBoxItem)cmbSuppliers.SelectedItem)?.Content.ToString() ?? "مورد";
                    // AddToSupplierAccount(supplierName, remainingAmount, "مشتريات - فاتورة رقم " + invoiceNumber);
                    Console.WriteLine($"تم ترحيل مبلغ {remainingAmount:N2} ر.س إلى حساب المورد {supplierName}");
                }

                MessageBox.Show($"تم حفظ الفاتورة بنجاح بطريقة دفع: {paymentMethod}\nالمبلغ المسدد: {paidAmount:N2} ر.س\nالمبلغ المتبقي: {remainingAmount:N2} ر.س", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSaveAndPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود منتجات في الفاتورة
                if (_items.Count == 0)
                {
                    MessageBox.Show("لا يمكن حفظ الفاتورة لأنها لا تحتوي على أي منتجات", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // الحصول على طريقة الدفع والمبلغ المسدد
                string paymentMethod = "نقدًا";
                double paidAmount = _totalAmount;
                double remainingAmount = 0;

                if (rbPartial.IsChecked == true)
                {
                    paymentMethod = "جزئي";
                    if (double.TryParse(txtPaidAmount.Text, NumberStyles.Any, CultureInfo.InvariantCulture, out paidAmount))
                    {
                        remainingAmount = _totalAmount - paidAmount;
                    }
                    else
                    {
                        MessageBox.Show("الرجاء إدخال مبلغ مسدد صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        txtPaidAmount.Focus();
                        return;
                    }
                }
                else if (rbCredit.IsChecked == true)
                {
                    paymentMethod = "آجل";
                    paidAmount = 0;
                    remainingAmount = _totalAmount;
                }

                // في التطبيق الحقيقي، سيتم حفظ الفاتورة في قاعدة البيانات
                // SaveInvoiceToDatabase(supplier, items, paymentMethod, paidAmount, remainingAmount);

                // ترحيل المبالغ إلى الحسابات المناسبة
                if (paidAmount > 0)
                {
                    // ترحيل المبلغ المسدد إلى حساب الخزينة
                    // AddToTreasury(paidAmount, "مشتريات - فاتورة رقم " + invoiceNumber);
                    Console.WriteLine($"تم ترحيل مبلغ {paidAmount:N2} ر.س إلى حساب الخزينة");
                }

                if (remainingAmount > 0)
                {
                    // ترحيل المبلغ المتبقي إلى حساب المورد
                    string supplierName = ((ComboBoxItem)cmbSuppliers.SelectedItem)?.Content.ToString() ?? "مورد";
                    // AddToSupplierAccount(supplierName, remainingAmount, "مشتريات - فاتورة رقم " + invoiceNumber);
                    Console.WriteLine($"تم ترحيل مبلغ {remainingAmount:N2} ر.س إلى حساب المورد {supplierName}");
                }

                // طباعة الفاتورة
                PrintInvoice(paymentMethod, paidAmount, remainingAmount);

                MessageBox.Show($"تم حفظ الفاتورة وإرسالها للطباعة\nطريقة الدفع: {paymentMethod}\nالمبلغ المسدد: {paidAmount:N2} ر.س\nالمبلغ المتبقي: {remainingAmount:N2} ر.س", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ وطباعة الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معاينة طباعة الفاتورة
        /// </summary>
        private void btnPrintPreview_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود منتجات في الفاتورة
                if (_items.Count == 0)
                {
                    MessageBox.Show("لا يمكن طباعة الفاتورة لأنها لا تحتوي على أي منتجات", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // الحصول على طريقة الدفع والمبلغ المسدد
                string paymentMethod = "نقدًا";
                double paidAmount = _totalAmount;
                double remainingAmount = 0;

                if (rbPartial.IsChecked == true)
                {
                    paymentMethod = "جزئي";
                    if (double.TryParse(txtPaidAmount.Text, NumberStyles.Any, CultureInfo.InvariantCulture, out paidAmount))
                    {
                        remainingAmount = _totalAmount - paidAmount;
                    }
                    else
                    {
                        MessageBox.Show("الرجاء إدخال مبلغ مسدد صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        txtPaidAmount.Focus();
                        return;
                    }
                }
                else if (rbCredit.IsChecked == true)
                {
                    paymentMethod = "آجل";
                    paidAmount = 0;
                    remainingAmount = _totalAmount;
                }

                // إنشاء قالب الطباعة
                var printTemplate = CreateInvoicePrintTemplate(paymentMethod, paidAmount, remainingAmount);

                // عرض نافذة معاينة الطباعة
                var previewWindow = new PrintPreviewWindow(printTemplate, "فاتورة مشتريات");
                previewWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء معاينة طباعة الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة الفاتورة
        /// </summary>
        private void PrintInvoice(string paymentMethod, double paidAmount, double remainingAmount)
        {
            try
            {
                // إنشاء قالب الطباعة
                var printTemplate = CreateInvoicePrintTemplate(paymentMethod, paidAmount, remainingAmount);

                // طباعة الفاتورة
                PrintService.PrintInvoice(printTemplate, "فاتورة مشتريات");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء قالب طباعة الفاتورة
        /// </summary>
        private InvoicePrintTemplate CreateInvoicePrintTemplate(string paymentMethod, double paidAmount, double remainingAmount)
        {
            // إنشاء قالب الطباعة
            var printTemplate = new InvoicePrintTemplate();

            // الحصول على معلومات المورد
            string supplierName = ((ComboBoxItem)cmbSuppliers.SelectedItem)?.Content.ToString() ?? "مورد";
            string supplierPhone = txtPhone.Text;
            string supplierAddress = txtAddress.Text;

            // الحصول على معلومات الفاتورة
            string invoiceNumber = "PUR-" + DateTime.Now.ToString("yyyyMMdd-HHmmss");
            DateTime invoiceDate = DateTime.Now;

            // حساب إجماليات الفاتورة
            double subtotal = 0;
            double totalDiscount = 0;

            foreach (var item in _items)
            {
                subtotal += item.Price * item.Quantity;
                totalDiscount += item.Discount;
            }

            double taxRate = 0.15;
            double taxAmount = (subtotal - totalDiscount) * taxRate;
            double total = subtotal - totalDiscount + taxAmount;

            // إضافة رقم تسلسلي للمنتجات
            var itemsWithIndex = new List<dynamic>();
            int index = 1;

            foreach (var item in _items)
            {
                var itemWithIndex = new
                {
                    Index = index++,
                    item.Code,
                    item.Name,
                    item.Unit,
                    item.Quantity,
                    item.Price,
                    item.Discount,
                    item.Total
                };

                itemsWithIndex.Add(itemWithIndex);
            }

            // تهيئة قالب الطباعة
            printTemplate.InitializePurchaseInvoice(
                invoiceNumber,
                invoiceDate,
                supplierName,
                supplierPhone,
                supplierAddress,
                paymentMethod,
                itemsWithIndex,
                subtotal,
                totalDiscount,
                taxAmount,
                total,
                paidAmount,
                remainingAmount,
                txtNotes.Text
            );

            return printTemplate;
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إلغاء الفاتورة
                var result = MessageBox.Show("هل أنت متأكد من إلغاء الفاتورة؟ سيتم فقدان جميع البيانات المدخلة.", "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    this.DialogResult = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالجة تغيير طريقة الدفع
        /// </summary>
        private void PaymentMethod_Checked(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود منتجات في الفاتورة
                if (_items.Count == 0)
                {
                    return;
                }

                // تحديث حالة حقل المبلغ المسدد بناءً على طريقة الدفع المختارة
                if (rbCash.IsChecked == true)
                {
                    // نقدًا - المبلغ المسدد يساوي إجمالي الفاتورة
                    txtPaidAmount.IsEnabled = false;
                    txtPaidAmount.Text = _totalAmount.ToString("N2");
                }
                else if (rbPartial.IsChecked == true)
                {
                    // جزئي - تمكين حقل المبلغ المسدد للتعديل
                    txtPaidAmount.IsEnabled = true;
                    txtPaidAmount.Text = "0.00";
                    txtPaidAmount.Focus();
                    txtPaidAmount.SelectAll();
                }
                else if (rbCredit.IsChecked == true)
                {
                    // آجل - المبلغ المسدد يساوي صفر
                    txtPaidAmount.IsEnabled = false;
                    txtPaidAmount.Text = "0.00";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تغيير طريقة الدفع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالجة تغيير المبلغ المسدد
        /// </summary>
        private void txtPaidAmount_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (rbPartial.IsChecked == true && !string.IsNullOrWhiteSpace(txtPaidAmount.Text))
                {
                    // التحقق من صحة المبلغ المدخل
                    if (double.TryParse(txtPaidAmount.Text, NumberStyles.Any, CultureInfo.InvariantCulture, out double paidAmount))
                    {
                        // التأكد من أن المبلغ المسدد لا يتجاوز إجمالي الفاتورة
                        if (paidAmount > _totalAmount)
                        {
                            MessageBox.Show("المبلغ المسدد لا يمكن أن يتجاوز إجمالي الفاتورة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                            txtPaidAmount.Text = _totalAmount.ToString("N2");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تغيير المبلغ المسدد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إضافة مورد جديد
        /// </summary>
        private void btnAddNewSupplier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة إضافة مورد جديد
                var supplierWindow = new QuickSupplierWindow();
                if (supplierWindow.ShowDialog() == true)
                {
                    // إضافة المورد الجديد إلى قائمة الموردين
                    var newSupplier = supplierWindow.Supplier;

                    // في التطبيق الحقيقي، سيتم حفظ المورد في قاعدة البيانات
                    // SaveSupplierToDatabase(newSupplier);

                    // إضافة المورد إلى القائمة المنسدلة
                    ComboBoxItem newItem = new ComboBoxItem { Content = newSupplier.Name };
                    cmbSuppliers.Items.Add(newItem);
                    cmbSuppliers.SelectedItem = newItem;

                    // تحديث حقول المورد
                    txtPhone.Text = newSupplier.Phone;
                    txtAddress.Text = newSupplier.Address;

                    MessageBox.Show("تم إضافة المورد بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة المورد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class NewPurchaseItem
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Unit { get; set; }
        public double Quantity { get; set; }
        public double Price { get; set; }
        public double Discount { get; set; }
        public double Total { get; set; }
    }
}
