using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace InjazAcc.UI.Views.Purchases
{
    /// <summary>
    /// Interaction logic for SimplePurchasesPage.xaml
    /// </summary>
    public partial class SimplePurchasesPage : Page
    {
        public SimplePurchasesPage()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية لفواتير المشتريات
                var purchasesList = new List<SimplePurchaseInvoice>
                {
                    new SimplePurchaseInvoice { InvoiceNumber = "PUR-001", InvoiceDate = DateTime.Now.AddDays(-2), SupplierName = "شركة التوريدات العامة", TotalAmount = 4250.75m, PaidAmount = 4250.75m, RemainingAmount = 0, Status = "مدفوعة" },
                    new SimplePurchaseInvoice { InvoiceNumber = "PUR-002", InvoiceDate = DateTime.Now.AddDays(-5), SupplierName = "مؤسسة الإمداد", TotalAmount = 2850.50m, PaidAmount = 1500.00m, RemainingAmount = 1350.50m, Status = "مدفوعة جزئياً" },
                    new SimplePurchaseInvoice { InvoiceNumber = "PUR-003", InvoiceDate = DateTime.Now.AddDays(-8), SupplierName = "شركة المواد الأولية", TotalAmount = 6200.00m, PaidAmount = 0, RemainingAmount = 6200.00m, Status = "مؤكدة" },
                    new SimplePurchaseInvoice { InvoiceNumber = "PUR-004", InvoiceDate = DateTime.Now.AddDays(-12), SupplierName = "مؤسسة التجهيزات", TotalAmount = 3100.25m, PaidAmount = 3100.25m, RemainingAmount = 0, Status = "مدفوعة" },
                    new SimplePurchaseInvoice { InvoiceNumber = "PUR-005", InvoiceDate = DateTime.Now.AddDays(-15), SupplierName = "شركة المعدات الحديثة", TotalAmount = 2100.00m, PaidAmount = 1000.00m, RemainingAmount = 1100.00m, Status = "مدفوعة جزئياً" }
                };
                dgPurchases.ItemsSource = purchasesList;

                // بيانات تجريبية لمردودات المشتريات
                var returnsList = new List<SimplePurchaseReturnInvoice>
                {
                    new SimplePurchaseReturnInvoice { InvoiceNumber = "PRET-001", InvoiceDate = DateTime.Now.AddDays(-3), SupplierName = "شركة التوريدات العامة", OriginalInvoiceNumber = "PUR-001", TotalAmount = 550.25m, Status = "مؤكدة" },
                    new SimplePurchaseReturnInvoice { InvoiceNumber = "PRET-002", InvoiceDate = DateTime.Now.AddDays(-10), SupplierName = "مؤسسة التجهيزات", OriginalInvoiceNumber = "PUR-004", TotalAmount = 650.75m, Status = "مؤكدة" }
                };
                dgReturns.ItemsSource = returnsList;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnNewPurchase_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة فاتورة مشتريات جديدة
                var purchaseInvoiceWindow = new NewPurchaseInvoiceWindow();
                purchaseInvoiceWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnNewReturn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة مردودات مشتريات جديدة
                var purchaseReturnWindow = new PurchaseReturnWindow();
                purchaseReturnWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPurchasesReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة تقرير المشتريات
                var purchasesReportWindow = new PurchasesReportWindow();
                purchasesReportWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void dgPurchases_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (dgPurchases.SelectedItem != null)
                {
                    var invoice = dgPurchases.SelectedItem as SimplePurchaseInvoice;
                    if (invoice != null)
                    {
                        MessageBox.Show($"تم اختيار الفاتورة رقم {invoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void dgReturns_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (dgReturns.SelectedItem != null)
                {
                    var returnInvoice = dgReturns.SelectedItem as SimplePurchaseReturnInvoice;
                    if (returnInvoice != null)
                    {
                        MessageBox.Show($"تم اختيار المردود رقم {returnInvoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnViewInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var invoice = button.DataContext as SimplePurchaseInvoice;
                if (invoice != null)
                {
                    MessageBox.Show($"عرض الفاتورة رقم {invoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var invoice = button.DataContext as SimplePurchaseInvoice;
                if (invoice != null)
                {
                    MessageBox.Show($"تعديل الفاتورة رقم {invoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var invoice = button.DataContext as SimplePurchaseInvoice;
                if (invoice != null)
                {
                    MessageBox.Show($"طباعة الفاتورة رقم {invoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDeleteInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var invoice = button.DataContext as SimplePurchaseInvoice;
                if (invoice != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف الفاتورة رقم {invoice.InvoiceNumber}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (result == MessageBoxResult.Yes)
                    {
                        MessageBox.Show("تم حذف الفاتورة بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnViewReturn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var returnInvoice = button.DataContext as SimplePurchaseReturnInvoice;
                if (returnInvoice != null)
                {
                    MessageBox.Show($"عرض المردود رقم {returnInvoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintReturn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var returnInvoice = button.DataContext as SimplePurchaseReturnInvoice;
                if (returnInvoice != null)
                {
                    MessageBox.Show($"طباعة المردود رقم {returnInvoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnShowReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري عرض التقرير...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    // فئات البيانات
    public class SimplePurchaseInvoice
    {
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public string SupplierName { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string Status { get; set; }
    }

    public class SimplePurchaseReturnInvoice
    {
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public string SupplierName { get; set; }
        public string OriginalInvoiceNumber { get; set; }
        public decimal TotalAmount { get; set; }
        public string Status { get; set; }
    }
}
