using System;
using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج الفاتورة في النظام
    /// </summary>
    public class Invoice
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public InvoiceType Type { get; set; }
        public InvoiceStatus Status { get; set; }
        public decimal SubTotal { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string Notes { get; set; }
        
        // العلاقة مع العميل/المورد
        public int? CustomerId { get; set; }
        public virtual Customer Customer { get; set; }
        
        public int? SupplierId { get; set; }
        public virtual Supplier Supplier { get; set; }
        
        // العلاقة مع المستخدم الذي أنشأ الفاتورة
        public int UserId { get; set; }
        public virtual User User { get; set; }
        
        // العلاقة مع المخزن
        public int WarehouseId { get; set; }
        public virtual Warehouse Warehouse { get; set; }
        
        // العلاقة مع الفاتورة الأصلية (في حالة المردودات)
        public int? OriginalInvoiceId { get; set; }
        public virtual Invoice OriginalInvoice { get; set; }
        
        // العلاقة مع مردودات هذه الفاتورة
        public virtual ICollection<Invoice> ReturnInvoices { get; set; }
        
        // العلاقة مع عناصر الفاتورة
        public virtual ICollection<InvoiceItem> Items { get; set; }
        
        // العلاقة مع المدفوعات
        public virtual ICollection<Payment> Payments { get; set; }
    }
    
    /// <summary>
    /// أنواع الفواتير
    /// </summary>
    public enum InvoiceType
    {
        Purchase = 1,        // فاتورة مشتريات
        PurchaseReturn = 2,  // مردودات مشتريات
        Sale = 3,            // فاتورة مبيعات
        SaleReturn = 4       // مردودات مبيعات
    }
    
    /// <summary>
    /// حالات الفاتورة
    /// </summary>
    public enum InvoiceStatus
    {
        Draft = 1,       // مسودة
        Confirmed = 2,   // مؤكدة
        Paid = 3,        // مدفوعة بالكامل
        PartiallyPaid = 4, // مدفوعة جزئياً
        Cancelled = 5    // ملغاة
    }
}
