using Microsoft.Extensions.DependencyInjection;
using InjazAcc.Core.Interfaces;
using InjazAcc.Services.Helpers;
using InjazAcc.Services.Printing;

namespace InjazAcc.Services
{
    /// <summary>
    /// تسجيل الخدمات في حاوية الاعتمادية
    /// </summary>
    public static class DependencyInjection
    {
        /// <summary>
        /// إضافة خدمات التطبيق إلى حاوية الاعتمادية
        /// </summary>
        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            // تسجيل خدمة القيود المحاسبية
            services.AddScoped<IAccountingService, AccountingService>();

            // تسجيل خدمة المصادقة
            services.AddScoped<IAuthService, AuthService>();

            // تسجيل خدمة التقارير المالية
            services.AddScoped<IFinancialStatementsService, FinancialStatementsService>();

            // تسجيل خدمات المساعدة
            services.AddScoped<ExceptionHandlingService>();

            return services;
        }
    }
}
