using System;
using System.Collections.Generic;
using System.IO;
using System.Printing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Xps;
using System.Windows.Xps.Packaging;

namespace InjazAcc.Services.Printing
{
    /// <summary>
    /// خدمة الطباعة المركزية للتطبيق
    /// </summary>
    public static class PrintService
    {
        // إعدادات الطابعات
        private static string _invoicePrinterName;
        private static string _reportPrinterName;
        private static string _invoicePaperSize = "A4";
        private static string _reportPaperSize = "A4";
        private static bool _printLogo = true;
        private static bool _showPrintPreview = true;
        private static bool _autoPrint = false;

        /// <summary>
        /// تحديث إعدادات الطابعات
        /// </summary>
        public static void UpdatePrinterSettings(
            string invoicePrinter,
            string reportPrinter,
            string invoicePaperSize,
            string reportPaperSize,
            bool printLogo,
            bool showPrintPreview,
            bool autoPrint)
        {
            _invoicePrinterName = invoicePrinter;
            _reportPrinterName = reportPrinter;
            _invoicePaperSize = invoicePaperSize;
            _reportPaperSize = reportPaperSize;
            _printLogo = printLogo;
            _showPrintPreview = showPrintPreview;
            _autoPrint = autoPrint;
        }

        /// <summary>
        /// طباعة فاتورة
        /// </summary>
        public static void PrintInvoice(FrameworkElement content, string title = "فاتورة")
        {
            try
            {
                // تحديد الطابعة المستخدمة
                string printerName = string.IsNullOrEmpty(_invoicePrinterName) 
                    ? GetDefaultPrinterName() 
                    : _invoicePrinterName;

                // تحديد حجم الورق
                PrintTicket printTicket = GetPrintTicket(_invoicePaperSize);

                // طباعة المحتوى
                PrintContent(content, title, printerName, printTicket);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة تقرير
        /// </summary>
        public static void PrintReport(FrameworkElement content, string title = "تقرير")
        {
            try
            {
                // تحديد الطابعة المستخدمة
                string printerName = string.IsNullOrEmpty(_reportPrinterName) 
                    ? GetDefaultPrinterName() 
                    : _reportPrinterName;

                // تحديد حجم الورق
                PrintTicket printTicket = GetPrintTicket(_reportPaperSize);

                // طباعة المحتوى
                PrintContent(content, title, printerName, printTicket);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الحصول على اسم الطابعة الافتراضية
        /// </summary>
        private static string GetDefaultPrinterName()
        {
            try
            {
                LocalPrintServer printServer = new LocalPrintServer();
                return printServer.DefaultPrintQueue?.Name;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// الحصول على تذكرة الطباعة بناءً على حجم الورق
        /// </summary>
        private static PrintTicket GetPrintTicket(string paperSize)
        {
            PrintTicket printTicket = new PrintTicket();

            // تحديد حجم الورق
            switch (paperSize)
            {
                case "A4":
                    printTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA4);
                    break;
                case "A5":
                    printTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA5);
                    break;
                case "Letter":
                    printTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.NorthAmericaLetter);
                    break;
                case "Legal":
                    printTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.NorthAmericaLegal);
                    break;
                case "80mm (حراري)":
                    // حجم مخصص للطابعات الحرارية 80 مم
                    printTicket.PageMediaSize = new PageMediaSize(80, 297);
                    break;
                case "58mm (حراري)":
                    // حجم مخصص للطابعات الحرارية 58 مم
                    printTicket.PageMediaSize = new PageMediaSize(58, 297);
                    break;
                default:
                    printTicket.PageMediaSize = new PageMediaSize(PageMediaSizeName.ISOA4);
                    break;
            }

            return printTicket;
        }

        /// <summary>
        /// طباعة المحتوى
        /// </summary>
        private static void PrintContent(FrameworkElement content, string title, string printerName, PrintTicket printTicket)
        {
            try
            {
                // نسخ المحتوى لتجنب تعديل العنصر الأصلي
                FrameworkElement contentCopy = XamlReader.Parse(XamlWriter.Save(content)) as FrameworkElement;
                
                if (contentCopy == null)
                {
                    throw new InvalidOperationException("فشل في نسخ المحتوى للطباعة");
                }

                // تحديد حجم المحتوى
                contentCopy.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                contentCopy.Arrange(new Rect(new Point(0, 0), contentCopy.DesiredSize));
                contentCopy.UpdateLayout();

                // إنشاء مستند للطباعة
                FixedDocument document = new FixedDocument();
                PageContent pageContent = new PageContent();
                FixedPage fixedPage = new FixedPage();

                // تحديد حجم الصفحة بناءً على حجم الورق
                fixedPage.Width = printTicket.PageMediaSize.Width.Value;
                fixedPage.Height = printTicket.PageMediaSize.Height.Value;

                // إضافة المحتوى إلى الصفحة
                fixedPage.Children.Add(contentCopy);
                ((IAddChild)pageContent).AddChild(fixedPage);
                document.Pages.Add(pageContent);

                // إنشاء مستند XPS للطباعة
                string tempFileName = Path.GetTempFileName();
                XpsDocument xpsDocument = new XpsDocument(tempFileName, FileAccess.ReadWrite);
                XpsDocumentWriter xpsWriter = XpsDocument.CreateXpsDocumentWriter(xpsDocument);
                xpsWriter.Write(document);

                // إنشاء مشاهد الطباعة
                PrintDialog printDialog = new PrintDialog();
                
                // تعيين الطابعة إذا كانت محددة
                if (!string.IsNullOrEmpty(printerName))
                {
                    try
                    {
                        LocalPrintServer printServer = new LocalPrintServer();
                        PrintQueue printQueue = printServer.GetPrintQueue(printerName);
                        printDialog.PrintQueue = printQueue;
                    }
                    catch
                    {
                        // إذا لم يتم العثور على الطابعة، استخدم الطابعة الافتراضية
                    }
                }

                // تعيين تذكرة الطباعة
                printDialog.PrintTicket = printTicket;

                // عرض معاينة الطباعة أو الطباعة مباشرة
                if (_showPrintPreview)
                {
                    // عرض نافذة الطباعة
                    if (printDialog.ShowDialog() == true)
                    {
                        printDialog.PrintDocument(xpsDocument.GetFixedDocumentSequence().DocumentPaginator, title);
                    }
                }
                else if (_autoPrint)
                {
                    // طباعة تلقائية بدون عرض نافذة الطباعة
                    printDialog.PrintDocument(xpsDocument.GetFixedDocumentSequence().DocumentPaginator, title);
                }
                else
                {
                    // عرض نافذة الطباعة
                    if (printDialog.ShowDialog() == true)
                    {
                        printDialog.PrintDocument(xpsDocument.GetFixedDocumentSequence().DocumentPaginator, title);
                    }
                }

                // إغلاق المستند وحذف الملف المؤقت
                xpsDocument.Close();
                try { File.Delete(tempFileName); } catch { }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة المحتوى: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
