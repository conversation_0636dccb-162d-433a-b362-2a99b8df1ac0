using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Accounts
{
    /// <summary>
    /// Interaction logic for CashPage.xaml
    /// </summary>
    public partial class CashPage : Page
    {
        private ObservableCollection<CashTransaction> _cashTransactions;
        private double _cashBalance = 50000; // رصيد افتراضي للخزينة
        private double _totalIncome = 0;
        private double _totalExpenses = 0;

        public CashPage()
        {
            InitializeComponent();

            // تعيين التواريخ الافتراضية (الشهر الحالي)
            dpFromDate.SelectedDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            dpToDate.SelectedDate = DateTime.Now;

            LoadSampleData();
            UpdateStatistics();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية لحركات الخزينة
                _cashTransactions = new ObservableCollection<CashTransaction>
                {
                    new CashTransaction { TransactionNumber = "C001", Date = DateTime.Now.AddDays(-25), Type = "إيراد", Description = "إيراد من مبيعات نقدية", Amount = 15000, RelatedAccount = "المبيعات" },
                    new CashTransaction { TransactionNumber = "C002", Date = DateTime.Now.AddDays(-20), Type = "مصروف", Description = "دفع إيجار المحل", Amount = 5000, RelatedAccount = "مصروف الإيجار" },
                    new CashTransaction { TransactionNumber = "C003", Date = DateTime.Now.AddDays(-18), Type = "تحصيل من عميل", Description = "تحصيل من العميل: شركة الأمل", Amount = 8000, RelatedAccount = "العملاء" },
                    new CashTransaction { TransactionNumber = "C004", Date = DateTime.Now.AddDays(-15), Type = "دفع لمورد", Description = "دفع للمورد: شركة التوريدات العامة", Amount = 12000, RelatedAccount = "الموردين" },
                    new CashTransaction { TransactionNumber = "C005", Date = DateTime.Now.AddDays(-12), Type = "إيراد", Description = "إيراد من خدمات استشارية", Amount = 7500, RelatedAccount = "إيرادات الخدمات" },
                    new CashTransaction { TransactionNumber = "C006", Date = DateTime.Now.AddDays(-10), Type = "مصروف", Description = "مصروفات كهرباء وماء", Amount = 1800, RelatedAccount = "مصروفات المرافق" },
                    new CashTransaction { TransactionNumber = "C007", Date = DateTime.Now.AddDays(-8), Type = "تحصيل من عميل", Description = "تحصيل من العميل: مؤسسة النور", Amount = 6500, RelatedAccount = "العملاء" },
                    new CashTransaction { TransactionNumber = "C008", Date = DateTime.Now.AddDays(-5), Type = "مصروف", Description = "رواتب الموظفين", Amount = 18000, RelatedAccount = "مصروف الرواتب" },
                    new CashTransaction { TransactionNumber = "C009", Date = DateTime.Now.AddDays(-3), Type = "دفع لمورد", Description = "دفع للمورد: مؤسسة الإمداد", Amount = 9500, RelatedAccount = "الموردين" },
                    new CashTransaction { TransactionNumber = "C010", Date = DateTime.Now.AddDays(-1), Type = "إيراد", Description = "إيراد من مبيعات نقدية", Amount = 12500, RelatedAccount = "المبيعات" }
                };

                dgCashTransactions.ItemsSource = _cashTransactions;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            try
            {
                _totalIncome = 0;
                _totalExpenses = 0;

                foreach (var transaction in _cashTransactions)
                {
                    if (transaction.Type == "إيراد" || transaction.Type == "تحصيل من عميل")
                    {
                        _totalIncome += transaction.Amount;
                    }
                    else if (transaction.Type == "مصروف" || transaction.Type == "دفع لمورد")
                    {
                        _totalExpenses += transaction.Amount;
                    }
                }

                // تحديث رصيد الخزينة (الرصيد الافتراضي + الإيرادات - المصروفات)
                _cashBalance = 50000 + _totalIncome - _totalExpenses;

                txtCashBalance.Text = _cashBalance.ToString("N2") + " ر.س";
                txtTotalIncome.Text = _totalIncome.ToString("N2") + " ر.س";
                txtTotalExpenses.Text = _totalExpenses.ToString("N2") + " ر.س";
                txtTransactionCount.Text = _cashTransactions.Count.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث الإحصائيات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DateTime? fromDate = dpFromDate.SelectedDate;
                DateTime? toDate = dpToDate.SelectedDate;
                string transactionType = (cmbTransactionType.SelectedItem as ComboBoxItem)?.Content.ToString();

                if (!fromDate.HasValue && !toDate.HasValue && (transactionType == "الكل" || transactionType == null))
                {
                    dgCashTransactions.ItemsSource = _cashTransactions;
                    return;
                }

                var filteredTransactions = new ObservableCollection<CashTransaction>();

                foreach (var transaction in _cashTransactions)
                {
                    bool dateMatch = true;
                    if (fromDate.HasValue && transaction.Date < fromDate.Value)
                    {
                        dateMatch = false;
                    }
                    if (toDate.HasValue && transaction.Date > toDate.Value)
                    {
                        dateMatch = false;
                    }

                    bool typeMatch = transactionType == "الكل" || transactionType == null || transaction.Type == transactionType;

                    if (dateMatch && typeMatch)
                    {
                        filteredTransactions.Add(transaction);
                    }
                }

                dgCashTransactions.ItemsSource = filteredTransactions;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnShowAll_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                dpFromDate.SelectedDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                dpToDate.SelectedDate = DateTime.Now;
                cmbTransactionType.SelectedIndex = 0;
                dgCashTransactions.ItemsSource = _cashTransactions;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnAddIncome_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var incomeWindow = new IncomeWindow();
                if (incomeWindow.ShowDialog() == true)
                {
                    // إضافة الإيراد الجديد إلى القائمة
                    _cashTransactions.Insert(0, new CashTransaction
                    {
                        TransactionNumber = GenerateTransactionNumber(),
                        Date = incomeWindow.TransactionDate,
                        Type = "إيراد",
                        Description = incomeWindow.Description,
                        Amount = incomeWindow.Amount,
                        RelatedAccount = incomeWindow.Account
                    });

                    UpdateStatistics();
                    MessageBox.Show("تم إضافة الإيراد بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnAddExpense_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var expenseWindow = new ExpenseWindow();
                if (expenseWindow.ShowDialog() == true)
                {
                    // إضافة المصروف الجديد إلى القائمة
                    _cashTransactions.Insert(0, new CashTransaction
                    {
                        TransactionNumber = GenerateTransactionNumber(),
                        Date = expenseWindow.TransactionDate,
                        Type = "مصروف",
                        Description = expenseWindow.Description,
                        Amount = expenseWindow.Amount,
                        RelatedAccount = expenseWindow.Account
                    });

                    UpdateStatistics();
                    MessageBox.Show("تم إضافة المصروف بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPaySupplier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var supplierPaymentWindow = new SupplierPaymentWindow();
                if (supplierPaymentWindow.ShowDialog() == true)
                {
                    // إضافة الدفعة الجديدة إلى القائمة
                    _cashTransactions.Insert(0, new CashTransaction
                    {
                        TransactionNumber = GenerateTransactionNumber(),
                        Date = supplierPaymentWindow.TransactionDate,
                        Type = "دفع لمورد",
                        Description = $"دفع للمورد: {supplierPaymentWindow.SupplierName}",
                        Amount = supplierPaymentWindow.Amount,
                        RelatedAccount = "الموردين"
                    });

                    UpdateStatistics();
                    MessageBox.Show("تم إضافة الدفعة بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnReceiveFromCustomer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var customerReceiptWindow = new CustomerReceiptWindow();
                if (customerReceiptWindow.ShowDialog() == true)
                {
                    // إضافة التحصيل الجديد إلى القائمة
                    _cashTransactions.Insert(0, new CashTransaction
                    {
                        TransactionNumber = GenerateTransactionNumber(),
                        Date = customerReceiptWindow.TransactionDate,
                        Type = "تحصيل من عميل",
                        Description = $"تحصيل من العميل: {customerReceiptWindow.CustomerName}",
                        Amount = customerReceiptWindow.Amount,
                        RelatedAccount = "العملاء"
                    });

                    UpdateStatistics();
                    MessageBox.Show("تم إضافة التحصيل بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnTransactionDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var transaction = button.DataContext as CashTransaction;

                if (transaction != null)
                {
                    MessageBox.Show($"تفاصيل العملية رقم: {transaction.TransactionNumber}\n" +
                                   $"التاريخ: {transaction.Date:yyyy-MM-dd}\n" +
                                   $"النوع: {transaction.Type}\n" +
                                   $"البيان: {transaction.Description}\n" +
                                   $"المبلغ: {transaction.Amount:N2} ر.س\n" +
                                   $"الحساب المقابل: {transaction.RelatedAccount}",
                                   "تفاصيل العملية", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintTransaction_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var transaction = button.DataContext as CashTransaction;

                if (transaction != null)
                {
                    MessageBox.Show($"جاري طباعة سند العملية رقم: {transaction.TransactionNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GenerateTransactionNumber()
        {
            // توليد رقم عملية جديد
            Random random = new Random();
            return $"C{random.Next(1000, 9999)}";
        }

        private void btnBackToAccounts_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // العودة إلى صفحة الحسابات الرئيسية
                NavigationService?.Navigate(new AccountsPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء العودة للصفحة الرئيسية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class CashTransaction
    {
        public string TransactionNumber { get; set; }
        public DateTime Date { get; set; }
        public string Type { get; set; }
        public string Description { get; set; }
        public double Amount { get; set; }
        public string RelatedAccount { get; set; }
    }
}
