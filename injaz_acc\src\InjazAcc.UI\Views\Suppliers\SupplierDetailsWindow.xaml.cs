using System;
using System.Collections.ObjectModel;
using System.Windows;

namespace InjazAcc.UI.Views.Suppliers
{
    /// <summary>
    /// Interaction logic for SupplierDetailsWindow.xaml
    /// </summary>
    public partial class SupplierDetailsWindow : Window
    {
        private Supplier _supplier;
        private ObservableCollection<SupplierInvoice> _invoices;
        private ObservableCollection<SupplierPayment> _payments;
        private ObservableCollection<SupplierReturn> _returns;

        public SupplierDetailsWindow(Supplier supplier)
        {
            InitializeComponent();
            _supplier = supplier;
            LoadSupplierData();
            LoadSampleData();
        }

        private void LoadSupplierData()
        {
            // تحميل بيانات المورد
            if (_supplier != null)
            {
                txtSupplierName.Text = $"تفاصيل المورد: {_supplier.Name}";
                txtCode.Text = _supplier.Code;
                txtName.Text = _supplier.Name;
                txtPhone.Text = _supplier.Phone;
                txtEmail.Text = _supplier.Email;
                txtAddress.Text = _supplier.Address;
                txtBalance.Text = _supplier.Balance.ToString("N2") + " ر.س";
                
                // قيم افتراضية للعرض
                txtTotalPurchases.Text = "150,000.00 ر.س";
                txtLastTransaction.Text = DateTime.Now.AddDays(-10).ToString("yyyy-MM-dd");
            }
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية للفواتير
                _invoices = new ObservableCollection<SupplierInvoice>
                {
                    new SupplierInvoice { InvoiceNumber = "PUR-001", Date = DateTime.Now.AddDays(-30), Amount = 8000, Paid = 8000, Remaining = 0, Status = "مدفوعة" },
                    new SupplierInvoice { InvoiceNumber = "PUR-002", Date = DateTime.Now.AddDays(-25), Amount = 12000, Paid = 12000, Remaining = 0, Status = "مدفوعة" },
                    new SupplierInvoice { InvoiceNumber = "PUR-003", Date = DateTime.Now.AddDays(-20), Amount = 15000, Paid = 10000, Remaining = 5000, Status = "جزئية" },
                    new SupplierInvoice { InvoiceNumber = "PUR-004", Date = DateTime.Now.AddDays(-15), Amount = 10000, Paid = 0, Remaining = 10000, Status = "غير مدفوعة" },
                    new SupplierInvoice { InvoiceNumber = "PUR-005", Date = DateTime.Now.AddDays(-10), Amount = 18000, Paid = 8000, Remaining = 10000, Status = "جزئية" }
                };
                dgInvoices.ItemsSource = _invoices;

                // بيانات تجريبية للمدفوعات
                _payments = new ObservableCollection<SupplierPayment>
                {
                    new SupplierPayment { ReceiptNumber = "PAY-001", Date = DateTime.Now.AddDays(-28), Amount = 8000, PaymentMethod = "تحويل بنكي", Notes = "دفعة فاتورة PUR-001" },
                    new SupplierPayment { ReceiptNumber = "PAY-002", Date = DateTime.Now.AddDays(-23), Amount = 12000, PaymentMethod = "شيك", Notes = "دفعة فاتورة PUR-002" },
                    new SupplierPayment { ReceiptNumber = "PAY-003", Date = DateTime.Now.AddDays(-18), Amount = 10000, PaymentMethod = "تحويل بنكي", Notes = "دفعة جزئية فاتورة PUR-003" },
                    new SupplierPayment { ReceiptNumber = "PAY-004", Date = DateTime.Now.AddDays(-8), Amount = 8000, PaymentMethod = "نقدي", Notes = "دفعة جزئية فاتورة PUR-005" }
                };
                dgPayments.ItemsSource = _payments;

                // بيانات تجريبية للمرتجعات
                _returns = new ObservableCollection<SupplierReturn>
                {
                    new SupplierReturn { ReturnNumber = "RET-001", Date = DateTime.Now.AddDays(-22), OriginalInvoiceNumber = "PUR-002", Amount = 2000, Notes = "مرتجع جزئي - بضاعة تالفة" },
                    new SupplierReturn { ReturnNumber = "RET-002", Date = DateTime.Now.AddDays(-12), OriginalInvoiceNumber = "PUR-003", Amount = 3000, Notes = "مرتجع جزئي - خطأ في الطلبية" }
                };
                dgReturns.ItemsSource = _returns;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnViewInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var invoice = (dgInvoices.SelectedItem as SupplierInvoice);
                if (invoice != null)
                {
                    MessageBox.Show($"عرض الفاتورة رقم: {invoice.InvoiceNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    // في التطبيق الحقيقي، سيتم فتح نافذة تفاصيل الفاتورة
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnViewReceipt_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var payment = (dgPayments.SelectedItem as SupplierPayment);
                if (payment != null)
                {
                    MessageBox.Show($"عرض سند الصرف رقم: {payment.ReceiptNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    // في التطبيق الحقيقي، سيتم فتح نافذة تفاصيل سند الصرف
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnViewReturn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var returnItem = (dgReturns.SelectedItem as SupplierReturn);
                if (returnItem != null)
                {
                    MessageBox.Show($"عرض المرتجع رقم: {returnItem.ReturnNumber}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    // في التطبيق الحقيقي، سيتم فتح نافذة تفاصيل المرتجع
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditSupplier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var supplierWindow = new SupplierWindow(_supplier);
                if (supplierWindow.ShowDialog() == true)
                {
                    _supplier = supplierWindow.Supplier;
                    LoadSupplierData();
                    MessageBox.Show("تم تعديل بيانات المورد بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintStatement_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري طباعة كشف حساب المورد...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                // في التطبيق الحقيقي، سيتم طباعة كشف حساب المورد
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    public class SupplierInvoice
    {
        public string InvoiceNumber { get; set; }
        public DateTime Date { get; set; }
        public double Amount { get; set; }
        public double Paid { get; set; }
        public double Remaining { get; set; }
        public string Status { get; set; }
    }

    public class SupplierPayment
    {
        public string ReceiptNumber { get; set; }
        public DateTime Date { get; set; }
        public double Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string Notes { get; set; }
    }

    public class SupplierReturn
    {
        public string ReturnNumber { get; set; }
        public DateTime Date { get; set; }
        public string OriginalInvoiceNumber { get; set; }
        public double Amount { get; set; }
        public string Notes { get; set; }
    }
}
