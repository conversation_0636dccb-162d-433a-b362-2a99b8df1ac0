﻿using System;
using System.Configuration;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using InjazAcc.Core.Interfaces;
using InjazAcc.DataAccess;
using InjazAcc.Services;

namespace InjazAcc.UI;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    // حاوية الاعتمادية
    private static IServiceProvider ServiceProvider;
    private static IConfiguration Configuration;

    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // تحميل ملف الإعدادات
        LoadConfiguration();

        // تهيئة حاوية الاعتمادية
        ConfigureServices();

        // تهيئة قاعدة البيانات
        InitializeDatabase();

        // إضافة معالج للاستثناءات غير المعالجة
        AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
        this.DispatcherUnhandledException += App_DispatcherUnhandledException;
    }

    /// <summary>
    /// تحميل ملف الإعدادات
    /// </summary>
    private void LoadConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

        Configuration = builder.Build();
    }

    /// <summary>
    /// تهيئة حاوية الاعتمادية وتسجيل الخدمات
    /// </summary>
    private void ConfigureServices()
    {
        var services = new ServiceCollection();

        // تسجيل Configuration
        services.AddSingleton<IConfiguration>(Configuration);

        // تسجيل سياق قاعدة البيانات
        var connectionString = Configuration.GetConnectionString("DefaultConnection");
        services.AddDbContext<InjazAccDbContext>(options =>
            options.UseSqlite(connectionString));

        // تسجيل وحدة العمل
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        // تسجيل خدمات التطبيق
        services.AddApplicationServices();

        // بناء حاوية الاعتمادية
        ServiceProvider = services.BuildServiceProvider();
    }

    /// <summary>
    /// تهيئة قاعدة البيانات وإنشاء الجداول
    /// </summary>
    private void InitializeDatabase()
    {
        try
        {
            using var scope = ServiceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<InjazAccDbContext>();

            // إنشاء قاعدة البيانات والجداول إذا لم تكن موجودة
            context.Database.EnsureCreated();

            // إضافة البيانات الأولية
            SeedDatabase(context);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// إضافة البيانات الأولية لقاعدة البيانات
    /// </summary>
    private void SeedDatabase(InjazAccDbContext context)
    {
        try
        {
            // إضافة المستخدم الافتراضي إذا لم يكن موجوداً
            if (!context.Users.Any())
            {
                var adminRole = new InjazAcc.Core.Models.Role
                {
                    Name = "مدير النظام",
                    Description = "صلاحيات كاملة للنظام"
                };
                context.Roles.Add(adminRole);

                var adminUser = new InjazAcc.Core.Models.User
                {
                    Username = "admin",
                    Email = "<EMAIL>",
                    PasswordHash = "admin123", // في التطبيق الحقيقي يجب تشفير كلمة المرور
                    FullName = "مدير النظام",
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
                context.Users.Add(adminUser);

                // إضافة معلومات الشركة الافتراضية
                var companyInfo = new InjazAcc.Core.Models.CompanyInfo
                {
                    Name = "شركة نظام إنجاز المحاسبي",
                    LegalName = "شركة إنجاز للبرمجيات",
                    TaxNumber = "*********",
                    CommercialRegister = "*********",
                    Address = "الرياض، المملكة العربية السعودية",
                    Phone = "0555555555",
                    Email = "<EMAIL>",
                    Website = "www.injaz-acc.com",
                    Currency = "ريال سعودي",
                    CurrencySymbol = "ر.س",
                    EstablishmentDate = new DateTime(2023, 1, 1)
                };
                context.CompanyInfo.Add(companyInfo);

                // إضافة السنة المالية الافتراضية
                var fiscalYear = new InjazAcc.Core.Models.FiscalYear
                {
                    Name = $"السنة المالية {DateTime.Now.Year}",
                    StartDate = new DateTime(DateTime.Now.Year, 1, 1),
                    EndDate = new DateTime(DateTime.Now.Year, 12, 31),
                    IsActive = true,
                    IsClosed = false
                };
                context.FiscalYears.Add(fiscalYear);

                // إضافة المخزن الافتراضي
                var defaultWarehouse = new InjazAcc.Core.Models.Warehouse
                {
                    Name = "المخزن الرئيسي",
                    Location = "المقر الرئيسي",
                    IsActive = true
                };
                context.Warehouses.Add(defaultWarehouse);

                // إضافة وحدة القياس الافتراضية
                var defaultUnit = new InjazAcc.Core.Models.Unit
                {
                    Name = "قطعة",
                    Symbol = "قطعة"
                };
                context.Units.Add(defaultUnit);

                // إضافة فئة المنتجات الافتراضية
                var defaultCategory = new InjazAcc.Core.Models.Category
                {
                    Name = "منتجات عامة",
                    Description = "فئة عامة للمنتجات"
                };
                context.Categories.Add(defaultCategory);

                context.SaveChanges();
            }
        }
        catch (Exception ex)
        {
            // تسجيل الخطأ
            LogError($"خطأ في إضافة البيانات الأولية: {ex.Message}");
        }
    }

    /// <summary>
    /// الحصول على خدمة من حاوية الاعتمادية
    /// </summary>
    public static T GetService<T>()
    {
        return ServiceProvider.GetService<T>();
    }

    /// <summary>
    /// الحصول على Configuration
    /// </summary>
    public static IConfiguration GetConfiguration()
    {
        return Configuration;
    }

    private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        try
        {
            Exception ex = (Exception)e.ExceptionObject;
            string errorMessage = $"حدث خطأ غير متوقع: {ex.Message}\n\nStack Trace:\n{ex.StackTrace}";

            // كتابة الخطأ في ملف سجل الأخطاء
            LogError(errorMessage);

            MessageBox.Show(errorMessage, "خطأ غير متوقع", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        catch
        {
            // في حالة حدوث خطأ أثناء معالجة الخطأ الأصلي
            MessageBox.Show("حدث خطأ غير متوقع في التطبيق.", "خطأ غير متوقع", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        try
        {
            string errorMessage = $"حدث خطأ غير متوقع: {e.Exception.Message}\n\nStack Trace:\n{e.Exception.StackTrace}";

            // كتابة الخطأ في ملف سجل الأخطاء
            LogError(errorMessage);

            MessageBox.Show(errorMessage, "خطأ غير متوقع", MessageBoxButton.OK, MessageBoxImage.Error);

            // تعليم الاستثناء كمعالج
            e.Handled = true;
        }
        catch
        {
            // في حالة حدوث خطأ أثناء معالجة الخطأ الأصلي
            MessageBox.Show("حدث خطأ غير متوقع في التطبيق.", "خطأ غير متوقع", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LogError(string errorMessage)
    {
        try
        {
            string logFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");

            // إنشاء مجلد السجلات إذا لم يكن موجوداً
            if (!Directory.Exists(logFolder))
            {
                Directory.CreateDirectory(logFolder);
            }

            string logFile = Path.Combine(logFolder, $"ErrorLog_{DateTime.Now:yyyy-MM-dd}.txt");
            string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {errorMessage}\n\n";

            // كتابة الخطأ في ملف السجل
            File.AppendAllText(logFile, logEntry);
        }
        catch
        {
            // تجاهل أي خطأ أثناء كتابة السجل
        }
    }
}

