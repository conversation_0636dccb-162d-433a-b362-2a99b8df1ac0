# InjazAcc - Accounting Management System

## Overview
InjazAcc (إنجاز المحاسبي) is a comprehensive accounting management system built with .NET 8 and WPF. It provides complete business management functionality including sales, purchases, inventory, customers, suppliers, and financial reporting.

## Features
- **Customer Management**: Add, edit, delete, and manage customer information
- **Supplier Management**: Complete supplier database with contact details
- **Inventory Management**: Product catalog with stock tracking
- **Sales Management**: Create and manage sales invoices
- **Purchase Management**: Handle purchase orders and invoices
- **Financial Reports**: Generate various financial reports
- **User Authentication**: Secure login system
- **Dashboard**: Overview of business metrics and statistics

## Technology Stack
- **.NET 8**: Core framework
- **WPF**: User interface framework
- **Entity Framework Core**: Database ORM
- **SQLite**: Database engine
- **C#**: Programming language

## Project Structure
```
injaz_acc/
├── src/
│   ├── InjazAcc.Core/           # Domain models and interfaces
│   ├── InjazAcc.DataAccess/     # Data access layer with EF Core
│   ├── InjazAcc.Business/       # Business logic layer
│   └── InjazAcc.UI/            # WPF user interface
├── tests/                       # Unit tests
└── docs/                       # Documentation
```

## Getting Started

### Prerequisites
- .NET 8.0 SDK or Runtime
- Visual Studio 2022 (recommended) or VS Code
- Windows 10/11

### Installation
1. Clone or download the repository
2. Open the solution in Visual Studio
3. Restore NuGet packages
4. Build the solution
5. Run the application

### Running the Application

#### Method 1: Visual Studio
1. Open the solution in Visual Studio
2. Set `InjazAcc.UI` as startup project
3. Press F5 or click "Start Debugging"

#### Method 2: Command Line
```bash
cd src/InjazAcc.UI
dotnet run
```

### Default Login Credentials
- **Username**: admin
- **Password**: admin123

## Database
- **Type**: SQLite
- **Location**: `src/InjazAcc.UI/InjazAccDb.db`
- **Auto-created**: Database is created automatically on first run
- **Migrations**: Entity Framework migrations handle schema updates

## Key Components

### Core Models
- Customer
- Supplier
- Product
- Invoice
- Payment
- Account
- User

### Data Access
- Repository pattern implementation
- Unit of Work pattern
- Entity Framework Core with SQLite
- Automatic migrations

### Business Layer
- Service classes for business logic
- Validation and business rules
- Data transformation

### UI Layer
- WPF with MVVM-like patterns
- Responsive design
- Arabic language support
- Modern UI components

## Usage

### Customer Management
1. Navigate to "العملاء" (Customers)
2. Add new customers with contact details
3. Edit or delete existing customers
4. View customer transaction history

### Supplier Management
1. Navigate to "الموردين" (Suppliers)
2. Manage supplier database
3. Track supplier balances
4. Handle supplier payments

### Inventory Management
1. Navigate to "المخزون" (Inventory)
2. Add products with pricing
3. Track stock levels
4. Manage product categories

### Sales & Purchases
1. Create invoices for sales/purchases
2. Select customers/suppliers
3. Add products to invoices
4. Generate and print invoices

## Data Persistence
All data is automatically saved to the SQLite database:
- Customer, supplier, and product information
- Sales and purchase transactions
- User authentication data
- System settings and configurations

## Backup & Recovery
- Database file: `InjazAccDb.db`
- Regular backups recommended
- Simple file copy for backup
- Restore by replacing database file

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License
This project is licensed under the MIT License.

## Support
For technical support or questions, please contact the development team.

## Version History
- **v1.0.0** (2025-08-02): Initial release
  - Complete accounting system
  - Customer and supplier management
  - Inventory tracking
  - Sales and purchase management
  - Financial reporting
  - User authentication
  - Arabic language support
