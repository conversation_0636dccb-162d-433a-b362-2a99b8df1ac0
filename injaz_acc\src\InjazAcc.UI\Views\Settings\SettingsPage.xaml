<Page x:Class="InjazAcc.UI.Views.Settings.SettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Settings"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="1100"
      Title="صفحة الإعدادات">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة -->
        <TextBlock Grid.Row="0" Text="إعدادات النظام" Style="{StaticResource PageTitle}"/>

        <!-- محتوى الصفحة -->
        <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}" FlowDirection="RightToLeft">
            <!-- إعدادات الشركة -->
            <TabItem Header="معلومات الشركة">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- بطاقة معلومات الشركة الأساسية -->
                        <materialDesign:Card Grid.Row="0" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="معلومات الشركة الأساسية" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>

                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- العمود الأول -->
                                    <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,10,10">
                                        <TextBlock Text="اسم الشركة" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtCompanyName" Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,0,10,10">
                                        <TextBlock Text="الاسم القانوني" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtLegalName" Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="0" Grid.Row="2" Margin="0,0,10,10">
                                        <TextBlock Text="الرقم الضريبي" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtTaxNumber" Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="0" Grid.Row="3" Margin="0,0,10,10">
                                        <TextBlock Text="السجل التجاري" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtCommercialRegister" Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="0" Grid.Row="4" Margin="0,0,10,10">
                                        <TextBlock Text="تاريخ التأسيس" Style="{StaticResource FormLabel}"/>
                                        <DatePicker x:Name="dpEstablishmentDate" Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>
                                    </StackPanel>

                                    <!-- العمود الثاني -->
                                    <StackPanel Grid.Column="1" Grid.Row="0" Margin="10,0,0,10">
                                        <TextBlock Text="العنوان" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtAddress" Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="1" Margin="10,0,0,10">
                                        <TextBlock Text="رقم الهاتف" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtPhone" Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="2" Margin="10,0,0,10">
                                        <TextBlock Text="البريد الإلكتروني" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtEmail" Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="3" Margin="10,0,0,10">
                                        <TextBlock Text="الموقع الإلكتروني" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtWebsite" Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="4" Margin="10,0,0,10">
                                        <TextBlock Text="شعار الشركة" Style="{StaticResource FormLabel}"/>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBox Grid.Column="0" x:Name="txtLogoPath" Style="{StaticResource MaterialDesignOutlinedTextBox}" IsReadOnly="True"/>
                                            <Button Grid.Column="1" Content="استعراض" Margin="10,0,0,0" Click="btnBrowseLogo_Click"/>
                                        </Grid>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </materialDesign:Card>

                        <!-- بطاقة إعدادات العملة -->
                        <materialDesign:Card Grid.Row="1" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="إعدادات العملة" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>

                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                        <TextBlock Text="العملة الأساسية" Style="{StaticResource FormLabel}"/>
                                        <ComboBox x:Name="cmbCurrency" Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                            <ComboBoxItem Content="ريال سعودي (SAR)" IsSelected="True"/>
                                            <ComboBoxItem Content="دولار أمريكي (USD)"/>
                                            <ComboBoxItem Content="يورو (EUR)"/>
                                            <ComboBoxItem Content="درهم إماراتي (AED)"/>
                                            <ComboBoxItem Content="دينار كويتي (KWD)"/>
                                            <ComboBoxItem Content="جنيه مصري (EGP)"/>
                                        </ComboBox>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                        <TextBlock Text="رمز العملة" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtCurrencySymbol" Style="{StaticResource MaterialDesignOutlinedTextBox}" Text="ر.س"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </materialDesign:Card>

                        <!-- أزرار الإجراءات -->
                        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                            <Button x:Name="btnSaveCompanyInfo" Content="حفظ المعلومات" Style="{StaticResource ActionButton}" Click="btnSaveCompanyInfo_Click"/>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <!-- إعدادات النظام العامة -->
            <TabItem Header="إعدادات عامة">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- بطاقة الإعدادات العامة -->
                        <materialDesign:Card Grid.Row="0" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="الإعدادات العامة" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>

                                <StackPanel Grid.Row="1">
                                    <CheckBox x:Name="chkAutoBackup" Content="تفعيل النسخ الاحتياطي التلقائي" Margin="0,5"/>
                                    <CheckBox x:Name="chkShowWelcomeScreen" Content="عرض شاشة الترحيب عند بدء التشغيل" Margin="0,5"/>
                                    <CheckBox x:Name="chkAutoUpdate" Content="تفعيل التحديث التلقائي" Margin="0,5"/>
                                    <CheckBox x:Name="chkSendNotifications" Content="إرسال إشعارات النظام" Margin="0,5"/>
                                </StackPanel>
                            </Grid>
                        </materialDesign:Card>

                        <!-- بطاقة إعدادات الفواتير -->
                        <materialDesign:Card Grid.Row="1" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="إعدادات الفواتير" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>

                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,10,10">
                                        <TextBlock Text="بادئة رقم فاتورة المبيعات" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtSalesInvoicePrefix" Style="{StaticResource MaterialDesignOutlinedTextBox}" Text="INV-"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="0" Margin="10,0,0,10">
                                        <TextBlock Text="بادئة رقم فاتورة المشتريات" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtPurchaseInvoicePrefix" Style="{StaticResource MaterialDesignOutlinedTextBox}" Text="PUR-"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,0,10,0">
                                        <TextBlock Text="نسبة الضريبة الافتراضية" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtDefaultTaxRate" Style="{StaticResource MaterialDesignOutlinedTextBox}" Text="15"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="1" Margin="10,0,0,0">
                                        <TextBlock Text="فترة السداد الافتراضية (بالأيام)" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtDefaultPaymentPeriod" Style="{StaticResource MaterialDesignOutlinedTextBox}" Text="30"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </materialDesign:Card>

                        <!-- بطاقة إعدادات الطباعة -->
                        <materialDesign:Card Grid.Row="2" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="إعدادات الطباعة" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>

                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- الطابعة الافتراضية للفواتير -->
                                    <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,10,10">
                                        <TextBlock Text="طابعة الفواتير" Style="{StaticResource FormLabel}"/>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <ComboBox Grid.Column="0" x:Name="cmbInvoicePrinter" Style="{StaticResource MaterialDesignOutlinedComboBox}"/>
                                            <Button Grid.Column="1" x:Name="btnRefreshInvoicePrinters" Margin="5,0,0,0"
                                                    ToolTip="تحديث قائمة الطابعات" Click="btnRefreshPrinters_Click">
                                                <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20"/>
                                            </Button>
                                        </Grid>
                                    </StackPanel>

                                    <!-- الطابعة الافتراضية للتقارير -->
                                    <StackPanel Grid.Column="1" Grid.Row="0" Margin="10,0,0,10">
                                        <TextBlock Text="طابعة التقارير" Style="{StaticResource FormLabel}"/>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <ComboBox Grid.Column="0" x:Name="cmbReportPrinter" Style="{StaticResource MaterialDesignOutlinedComboBox}"/>
                                            <Button Grid.Column="1" x:Name="btnRefreshReportPrinters" Margin="5,0,0,0"
                                                    ToolTip="تحديث قائمة الطابعات" Click="btnRefreshPrinters_Click">
                                                <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20"/>
                                            </Button>
                                        </Grid>
                                    </StackPanel>

                                    <!-- حجم الورق للفواتير -->
                                    <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,10,10,10">
                                        <TextBlock Text="حجم ورق الفواتير" Style="{StaticResource FormLabel}"/>
                                        <ComboBox x:Name="cmbInvoicePaperSize" Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                            <ComboBoxItem Content="A4" IsSelected="True"/>
                                            <ComboBoxItem Content="A5"/>
                                            <ComboBoxItem Content="80mm (حراري)"/>
                                            <ComboBoxItem Content="58mm (حراري)"/>
                                        </ComboBox>
                                    </StackPanel>

                                    <!-- حجم الورق للتقارير -->
                                    <StackPanel Grid.Column="1" Grid.Row="1" Margin="10,10,0,10">
                                        <TextBlock Text="حجم ورق التقارير" Style="{StaticResource FormLabel}"/>
                                        <ComboBox x:Name="cmbReportPaperSize" Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                            <ComboBoxItem Content="A4" IsSelected="True"/>
                                            <ComboBoxItem Content="A5"/>
                                            <ComboBoxItem Content="Letter"/>
                                            <ComboBoxItem Content="Legal"/>
                                        </ComboBox>
                                    </StackPanel>

                                    <!-- إعدادات إضافية للطباعة -->
                                    <StackPanel Grid.Column="0" Grid.ColumnSpan="2" Grid.Row="2" Margin="0,10,0,0">
                                        <CheckBox x:Name="chkPrintLogo" Content="طباعة شعار الشركة على الفواتير" Margin="0,5" IsChecked="True"/>
                                        <CheckBox x:Name="chkPrintPreview" Content="عرض معاينة قبل الطباعة" Margin="0,5" IsChecked="True"/>
                                        <CheckBox x:Name="chkAutoPrint" Content="طباعة تلقائية عند حفظ الفاتورة" Margin="0,5"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </materialDesign:Card>

                        <!-- أزرار الإجراءات -->
                        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                            <Button x:Name="btnSaveGeneralSettings" Content="حفظ الإعدادات" Style="{StaticResource ActionButton}" Click="btnSaveGeneralSettings_Click"/>
                            <Button x:Name="btnResetSettings" Content="استعادة الإعدادات الافتراضية" Style="{StaticResource ActionButton}" Margin="10,0,0,0" Click="btnResetSettings_Click"/>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <!-- إعدادات المستخدمين -->
            <TabItem Header="المستخدمين والصلاحيات">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان القسم -->
                    <TextBlock Grid.Row="0" Text="إدارة المستخدمين والصلاحيات" Style="{StaticResource SectionTitle}" Margin="0,0,0,15"/>

                    <!-- محتوى القسم -->
                    <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}">
                        <!-- قسم المستخدمين -->
                        <TabItem Header="المستخدمين">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- شريط البحث والإضافة -->
                                <Grid Grid.Row="0" Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox Grid.Column="0" x:Name="txtSearchUser" Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                             materialDesign:HintAssist.Hint="بحث عن مستخدم..." Margin="0,0,10,0"
                                             KeyUp="txtSearchUser_KeyUp"/>

                                    <Button Grid.Column="1" x:Name="btnAddUser" Content="إضافة مستخدم جديد"
                                            Style="{StaticResource ActionButton}" Click="btnAddUser_Click"/>
                                </Grid>

                                <!-- جدول المستخدمين -->
                                <DataGrid Grid.Row="1" x:Name="dgUsers" AutoGenerateColumns="False" IsReadOnly="False"
                                          Style="{StaticResource MaterialDesignDataGrid}" SelectionMode="Single"
                                          CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="False"
                                          CanUserResizeRows="False" CanUserSortColumns="True"
                                          MouseDoubleClick="dgUsers_MouseDoubleClick"
                                          CellEditEnding="dgUsers_CellEditEnding">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="اسم المستخدم" Binding="{Binding Username}" Width="*"/>
                                        <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding FullName}" Width="*"/>
                                        <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="*"/>
                                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding PhoneNumber}" Width="*"/>
                                        <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="70"/>
                                        <DataGridTemplateColumn Header="الإجراءات" Width="120" IsReadOnly="True">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal">
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="تعديل" Margin="0,0,5,0"
                                                                Click="btnEditUser_Click">
                                                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18"/>
                                                        </Button>
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="حذف" Foreground="Red"
                                                                Click="btnDeleteUser_Click">
                                                            <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                                        </Button>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>

                                <!-- إحصائيات المستخدمين -->
                                <Grid Grid.Row="2" Margin="0,10,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" Background="#E3F2FD" CornerRadius="5" Margin="0,0,5,0" Padding="10">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="AccountMultiple" Width="24" Height="24" VerticalAlignment="Center" Foreground="#1976D2"/>
                                            <TextBlock Text="إجمالي المستخدمين: " Margin="5,0" VerticalAlignment="Center"/>
                                            <TextBlock x:Name="txtTotalUsers" Text="0" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="1" Background="#E8F5E9" CornerRadius="5" Margin="5,0" Padding="10">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="AccountCheck" Width="24" Height="24" VerticalAlignment="Center" Foreground="#388E3C"/>
                                            <TextBlock Text="المستخدمين النشطين: " Margin="5,0" VerticalAlignment="Center"/>
                                            <TextBlock x:Name="txtActiveUsers" Text="0" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="2" Background="#FFEBEE" CornerRadius="5" Margin="5,0,0,0" Padding="10">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="AccountOff" Width="24" Height="24" VerticalAlignment="Center" Foreground="#D32F2F"/>
                                            <TextBlock Text="المستخدمين غير النشطين: " Margin="5,0" VerticalAlignment="Center"/>
                                            <TextBlock x:Name="txtInactiveUsers" Text="0" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>
                            </Grid>
                        </TabItem>

                        <!-- قسم الأدوار -->
                        <TabItem Header="الأدوار والصلاحيات">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- شريط البحث والإضافة -->
                                <Grid Grid.Row="0" Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox Grid.Column="0" x:Name="txtSearchRole" Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                             materialDesign:HintAssist.Hint="بحث عن دور..." Margin="0,0,10,0"
                                             KeyUp="txtSearchRole_KeyUp"/>

                                    <Button Grid.Column="1" x:Name="btnAddRole" Content="إضافة دور جديد"
                                            Style="{StaticResource ActionButton}" Click="btnAddRole_Click"/>
                                </Grid>

                                <!-- جدول الأدوار -->
                                <DataGrid Grid.Row="1" x:Name="dgRoles" AutoGenerateColumns="False" IsReadOnly="True"
                                          Style="{StaticResource MaterialDesignDataGrid}" SelectionMode="Single"
                                          CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="False"
                                          CanUserResizeRows="False" CanUserSortColumns="True"
                                          MouseDoubleClick="dgRoles_MouseDoubleClick">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="اسم الدور" Binding="{Binding Name}" Width="*"/>
                                        <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="2*"/>
                                        <DataGridTemplateColumn Header="الإجراءات" Width="180">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal">
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="تعديل" Margin="0,0,5,0"
                                                                Click="btnEditRole_Click">
                                                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18"/>
                                                        </Button>
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="إدارة الصلاحيات" Margin="0,0,5,0" Foreground="#1976D2"
                                                                Click="btnManagePermissions_Click">
                                                            <materialDesign:PackIcon Kind="ShieldAccount" Width="18" Height="18"/>
                                                        </Button>
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="حذف" Foreground="Red"
                                                                Click="btnDeleteRole_Click">
                                                            <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                                        </Button>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>

                                <!-- إحصائيات الأدوار -->
                                <Border Grid.Row="2" Background="#E3F2FD" CornerRadius="5" Margin="0,10,0,0" Padding="10">
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="ShieldAccount" Width="24" Height="24" VerticalAlignment="Center" Foreground="#1976D2"/>
                                        <TextBlock Text="إجمالي الأدوار: " Margin="5,0" VerticalAlignment="Center"/>
                                        <TextBlock x:Name="txtTotalRoles" Text="0" FontWeight="Bold" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </Grid>
                        </TabItem>

                        <!-- قسم الصلاحيات -->
                        <TabItem Header="قائمة الصلاحيات">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- شريط البحث -->
                                <TextBox Grid.Row="0" x:Name="txtSearchPermission" Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         materialDesign:HintAssist.Hint="بحث عن صلاحية..." Margin="0,0,0,10"
                                         KeyUp="txtSearchPermission_KeyUp"/>

                                <!-- جدول الصلاحيات -->
                                <DataGrid Grid.Row="1" x:Name="dgPermissions" AutoGenerateColumns="False" IsReadOnly="True"
                                          Style="{StaticResource MaterialDesignDataGrid}" SelectionMode="Single"
                                          CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="False"
                                          CanUserResizeRows="False" CanUserSortColumns="True">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="اسم الصلاحية" Binding="{Binding Name}" Width="*"/>
                                        <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="2*"/>
                                        <DataGridTextColumn Header="الوحدة" Binding="{Binding Module}" Width="*"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </TabItem>
                    </TabControl>
                </Grid>
            </TabItem>

            <!-- النسخ الاحتياطي -->
            <TabItem Header="النسخ الاحتياطي">
                <Grid Margin="15">
                    <TextBlock Text="سيتم تطوير هذا القسم في الإصدار القادم" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16" Foreground="Gray"/>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</Page>
