using InjazAcc.Core.Models;
using System;
using System.Threading.Tasks;

namespace InjazAcc.Core.Interfaces
{
    /// <summary>
    /// واجهة وحدة العمل للتعامل مع المستودعات
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        // المستودعات
        IRepository<User> Users { get; }
        IRepository<Role> Roles { get; }
        IRepository<Permission> Permissions { get; }
        IRepository<Product> Products { get; }
        IRepository<Category> Categories { get; }
        IRepository<Unit> Units { get; }
        IRepository<Warehouse> Warehouses { get; }
        IRepository<Invoice> Invoices { get; }
        IRepository<InvoiceItem> InvoiceItems { get; }
        IRepository<Customer> Customers { get; }
        IRepository<Supplier> Suppliers { get; }
        IRepository<Payment> Payments { get; }
        IRepository<Account> Accounts { get; }
        IRepository<JournalEntry> JournalEntries { get; }
        IRepository<JournalEntryItem> JournalEntryItems { get; }
        IRepository<AccountingSettings> AccountingSettings { get; }
        IRepository<Partner> Partners { get; }
        IRepository<FiscalYear> FiscalYears { get; }
        IRepository<CompanyInfo> CompanyInfo { get; }
        IRepository<SystemSettings> SystemSettings { get; }
        IRepository<AuditLog> AuditLogs { get; }

        // حفظ التغييرات
        Task<int> CompleteAsync();

        /// <summary>
        /// حفظ التغييرات في قاعدة البيانات
        /// </summary>
        Task<int> SaveChangesAsync();
    }
}
