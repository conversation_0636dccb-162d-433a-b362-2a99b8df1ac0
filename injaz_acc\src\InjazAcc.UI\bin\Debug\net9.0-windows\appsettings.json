{"ConnectionStrings": {"DefaultConnection": "Data Source=InjazAccDb.db"}, "AppSettings": {"ApplicationName": "نظام إنجاز المحاسبي", "Version": "1.0.0", "CompanyName": "شركة إنجاز للبرمجيات", "SupportEmail": "<EMAIL>", "DefaultLanguage": "ar-SA", "DefaultCurrency": "SAR", "CurrencySymbol": "ر.س"}, "DatabaseSettings": {"CommandTimeout": 30, "EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "AutoMigrate": true}, "SecuritySettings": {"PasswordMinLength": 6, "RequireDigit": true, "RequireLowercase": false, "RequireUppercase": false, "RequireNonAlphanumeric": false, "SessionTimeoutMinutes": 60, "MaxLoginAttempts": 5, "LockoutDurationMinutes": 15}, "InvoiceSettings": {"SalesInvoicePrefix": "INV-S-", "PurchaseInvoicePrefix": "INV-P-", "DefaultTaxRate": 15.0, "DefaultPaymentTermDays": 30, "AutoGenerateInvoiceNumber": true, "PrintLogoOnInvoice": true, "ShowPricesWithTax": true}, "InventorySettings": {"DefaultWarehouseId": 1, "AllowNegativeStock": false, "AutoUpdateCostPrice": true, "StockAlertThreshold": 10, "EnableBarcodeScanning": false}, "AccountingSettings": {"FiscalYearStartMonth": 1, "DefaultAccountingMethod": "Accrual", "EnableMultiCurrency": false, "AutoCreateJournalEntries": true, "RequireApprovalForJournalEntries": false}, "PrintSettings": {"DefaultPrinter": "", "InvoicePaperSize": "A4", "ReportPaperSize": "A4", "ShowPrintPreview": true, "AutoPrintInvoices": false, "PrintCompanyLogo": true}, "BackupSettings": {"AutoBackup": true, "BackupIntervalHours": 24, "BackupRetentionDays": 30, "BackupLocation": "Backups", "CompressBackups": true}, "NotificationSettings": {"EnableEmailNotifications": false, "EnableSMSNotifications": false, "NotifyOnLowStock": true, "NotifyOnOverduePayments": true, "NotifyOnSystemErrors": true}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "EnableFileLogging": true, "LogFilePath": "Logs", "MaxLogFileSizeMB": 10, "MaxLogFiles": 10}}