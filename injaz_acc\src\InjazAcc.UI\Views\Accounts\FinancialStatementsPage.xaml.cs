using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace InjazAcc.UI.Views.Accounts
{
    /// <summary>
    /// Interaction logic for FinancialStatementsPage.xaml
    /// </summary>
    public partial class FinancialStatementsPage : Page
    {
        private ObservableCollection<TrialBalanceItem> _trialBalanceItems;
        private ObservableCollection<FinancialStatementItem> _incomeStatementItems;
        private ObservableCollection<FinancialStatementItem> _assetsItems;
        private ObservableCollection<FinancialStatementItem> _liabilitiesEquityItems;

        public FinancialStatementsPage()
        {
            InitializeComponent();

            // تعيين التواريخ الافتراضية
            dpFromDate.SelectedDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            dpToDate.SelectedDate = DateTime.Now;

            // ربط حدث تغيير الفترة
            cmbPeriod.SelectionChanged += CmbPeriod_SelectionChanged;

            // تحميل البيانات التجريبية
            LoadSampleData();

            // عرض البيانات
            UpdateTrialBalance();
            UpdateIncomeStatement();
            UpdateBalanceSheet();
        }

        private void CmbPeriod_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // إظهار/إخفاء حقول التواريخ المخصصة
                if (cmbPeriod.SelectedIndex == 4) // فترة مخصصة
                {
                    spCustomDates.Visibility = Visibility.Visible;
                }
                else
                {
                    spCustomDates.Visibility = Visibility.Collapsed;

                    // تعيين التواريخ حسب الفترة المختارة
                    DateTime now = DateTime.Now;
                    switch (cmbPeriod.SelectedIndex)
                    {
                        case 0: // الربع الحالي
                            int currentQuarter = (now.Month - 1) / 3 + 1;
                            dpFromDate.SelectedDate = new DateTime(now.Year, (currentQuarter - 1) * 3 + 1, 1);
                            dpToDate.SelectedDate = now;
                            break;
                        case 1: // الربع السابق
                            int previousQuarter = ((now.Month - 1) / 3);
                            if (previousQuarter == 0)
                            {
                                previousQuarter = 4;
                                dpFromDate.SelectedDate = new DateTime(now.Year - 1, (previousQuarter - 1) * 3 + 1, 1);
                                dpToDate.SelectedDate = new DateTime(now.Year - 1, previousQuarter * 3, DateTime.DaysInMonth(now.Year - 1, previousQuarter * 3));
                            }
                            else
                            {
                                dpFromDate.SelectedDate = new DateTime(now.Year, (previousQuarter - 1) * 3 + 1, 1);
                                dpToDate.SelectedDate = new DateTime(now.Year, previousQuarter * 3, DateTime.DaysInMonth(now.Year, previousQuarter * 3));
                            }
                            break;
                        case 2: // السنة الحالية
                            dpFromDate.SelectedDate = new DateTime(now.Year, 1, 1);
                            dpToDate.SelectedDate = now;
                            break;
                        case 3: // السنة السابقة
                            dpFromDate.SelectedDate = new DateTime(now.Year - 1, 1, 1);
                            dpToDate.SelectedDate = new DateTime(now.Year - 1, 12, 31);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية لميزان المراجعة
                _trialBalanceItems = new ObservableCollection<TrialBalanceItem>
                {
                    new TrialBalanceItem { AccountNumber = "1010", AccountName = "الصندوق", DebitBalance = 35000, CreditBalance = 0 },
                    new TrialBalanceItem { AccountNumber = "1020", AccountName = "البنك", DebitBalance = 120000, CreditBalance = 0 },
                    new TrialBalanceItem { AccountNumber = "1030", AccountName = "المخزون", DebitBalance = 180000, CreditBalance = 0 },
                    new TrialBalanceItem { AccountNumber = "1040", AccountName = "العملاء", DebitBalance = 65000, CreditBalance = 0 },
                    new TrialBalanceItem { AccountNumber = "1050", AccountName = "أثاث ومعدات", DebitBalance = 120000, CreditBalance = 0 },
                    new TrialBalanceItem { AccountNumber = "2010", AccountName = "الموردين", DebitBalance = 0, CreditBalance = 75000 },
                    new TrialBalanceItem { AccountNumber = "2020", AccountName = "قروض قصيرة الأجل", DebitBalance = 0, CreditBalance = 100000 },
                    new TrialBalanceItem { AccountNumber = "2030", AccountName = "مصروفات مستحقة", DebitBalance = 0, CreditBalance = 25000 },
                    new TrialBalanceItem { AccountNumber = "3010", AccountName = "رأس المال", DebitBalance = 0, CreditBalance = 350000 },
                    new TrialBalanceItem { AccountNumber = "3020", AccountName = "الأرباح المحتجزة", DebitBalance = 0, CreditBalance = 25000 },
                    new TrialBalanceItem { AccountNumber = "4010", AccountName = "المبيعات", DebitBalance = 0, CreditBalance = 250000 },
                    new TrialBalanceItem { AccountNumber = "4020", AccountName = "إيرادات أخرى", DebitBalance = 0, CreditBalance = 15000 },
                    new TrialBalanceItem { AccountNumber = "5010", AccountName = "تكلفة المبيعات", DebitBalance = 150000, CreditBalance = 0 },
                    new TrialBalanceItem { AccountNumber = "5020", AccountName = "رواتب وأجور", DebitBalance = 85000, CreditBalance = 0 },
                    new TrialBalanceItem { AccountNumber = "5030", AccountName = "إيجارات", DebitBalance = 35000, CreditBalance = 0 },
                    new TrialBalanceItem { AccountNumber = "5040", AccountName = "مصروفات عمومية وإدارية", DebitBalance = 50000, CreditBalance = 0 }
                };

                // بيانات تجريبية لقائمة الدخل
                _incomeStatementItems = new ObservableCollection<FinancialStatementItem>
                {
                    new FinancialStatementItem { Description = "المبيعات", Amount = 250000 },
                    new FinancialStatementItem { Description = "إيرادات أخرى", Amount = 15000 },
                    new FinancialStatementItem { Description = "إجمالي الإيرادات", Amount = 265000, IsBold = true },
                    new FinancialStatementItem { Description = "تكلفة المبيعات", Amount = -150000 },
                    new FinancialStatementItem { Description = "مجمل الربح", Amount = 115000, IsBold = true },
                    new FinancialStatementItem { Description = "رواتب وأجور", Amount = -85000 },
                    new FinancialStatementItem { Description = "إيجارات", Amount = -35000 },
                    new FinancialStatementItem { Description = "مصروفات عمومية وإدارية", Amount = -50000 },
                    new FinancialStatementItem { Description = "إجمالي المصروفات", Amount = -170000, IsBold = true },
                    new FinancialStatementItem { Description = "صافي الربح (الخسارة)", Amount = -55000, IsBold = true }
                };

                // بيانات تجريبية للأصول
                _assetsItems = new ObservableCollection<FinancialStatementItem>
                {
                    new FinancialStatementItem { Description = "الأصول المتداولة", Amount = 0, IsHeader = true },
                    new FinancialStatementItem { Description = "الصندوق", Amount = 35000 },
                    new FinancialStatementItem { Description = "البنك", Amount = 120000 },
                    new FinancialStatementItem { Description = "المخزون", Amount = 180000 },
                    new FinancialStatementItem { Description = "العملاء", Amount = 65000 },
                    new FinancialStatementItem { Description = "إجمالي الأصول المتداولة", Amount = 400000, IsBold = true },
                    new FinancialStatementItem { Description = "الأصول الثابتة", Amount = 0, IsHeader = true },
                    new FinancialStatementItem { Description = "أثاث ومعدات", Amount = 120000 },
                    new FinancialStatementItem { Description = "إجمالي الأصول الثابتة", Amount = 120000, IsBold = true }
                };

                // بيانات تجريبية للخصوم وحقوق الملكية
                _liabilitiesEquityItems = new ObservableCollection<FinancialStatementItem>
                {
                    new FinancialStatementItem { Description = "الخصوم المتداولة", Amount = 0, IsHeader = true },
                    new FinancialStatementItem { Description = "الموردين", Amount = 75000 },
                    new FinancialStatementItem { Description = "قروض قصيرة الأجل", Amount = 100000 },
                    new FinancialStatementItem { Description = "مصروفات مستحقة", Amount = 25000 },
                    new FinancialStatementItem { Description = "إجمالي الخصوم المتداولة", Amount = 200000, IsBold = true },
                    new FinancialStatementItem { Description = "حقوق الملكية", Amount = 0, IsHeader = true },
                    new FinancialStatementItem { Description = "رأس المال", Amount = 350000 },
                    new FinancialStatementItem { Description = "الأرباح المحتجزة", Amount = 25000 },
                    new FinancialStatementItem { Description = "صافي ربح (خسارة) الفترة", Amount = -55000 },
                    new FinancialStatementItem { Description = "إجمالي حقوق الملكية", Amount = 320000, IsBold = true }
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateTrialBalance()
        {
            try
            {
                // التحقق من وجود البيانات قبل محاولة الوصول إليها
                if (_trialBalanceItems == null || _trialBalanceItems.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات لميزان المراجعة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                dgTrialBalance.ItemsSource = _trialBalanceItems;

                // حساب الإجماليات
                double totalDebit = 0;
                double totalCredit = 0;

                foreach (var item in _trialBalanceItems)
                {
                    totalDebit += item.DebitBalance;
                    totalCredit += item.CreditBalance;
                }

                txtTotalDebit.Text = totalDebit.ToString("N2");
                txtTotalCredit.Text = totalCredit.ToString("N2");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث ميزان المراجعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateIncomeStatement()
        {
            try
            {
                // التحقق من وجود البيانات قبل محاولة الوصول إليها
                if (_incomeStatementItems == null || _incomeStatementItems.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات لقائمة الدخل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                dgIncomeStatement.ItemsSource = _incomeStatementItems;

                // عرض صافي الربح/الخسارة
                double netIncome = 0;

                // البحث عن صافي الربح/الخسارة (آخر عنصر في القائمة)
                if (_incomeStatementItems.Count > 0)
                {
                    netIncome = _incomeStatementItems[_incomeStatementItems.Count - 1].Amount;
                }

                txtNetIncome.Text = netIncome.ToString("N2");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث قائمة الدخل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateBalanceSheet()
        {
            try
            {
                // التحقق من وجود البيانات قبل محاولة الوصول إليها
                if (_assetsItems == null || _liabilitiesEquityItems == null)
                {
                    MessageBox.Show("لا توجد بيانات للميزانية العمومية", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                dgAssets.ItemsSource = _assetsItems;
                dgLiabilitiesEquity.ItemsSource = _liabilitiesEquityItems;

                // التحقق من وجود العناصر المطلوبة قبل محاولة الوصول إليها
                double totalAssets = 0;
                double totalLiabilitiesEquity = 0;

                // حساب إجمالي الأصول
                if (_assetsItems.Count > 5 && _assetsItems.Count > 8)
                {
                    totalAssets = _assetsItems[5].Amount + _assetsItems[8].Amount; // إجمالي الأصول المتداولة + إجمالي الأصول الثابتة
                }
                else
                {
                    // حساب الإجمالي بطريقة بديلة إذا لم تكن العناصر المتوقعة موجودة
                    foreach (var item in _assetsItems)
                    {
                        if (item.IsBold && !item.IsHeader)
                        {
                            totalAssets += item.Amount;
                        }
                    }
                }

                // حساب إجمالي الخصوم وحقوق الملكية
                if (_liabilitiesEquityItems.Count > 4 && _liabilitiesEquityItems.Count > 9)
                {
                    totalLiabilitiesEquity = _liabilitiesEquityItems[4].Amount + _liabilitiesEquityItems[9].Amount; // إجمالي الخصوم المتداولة + إجمالي حقوق الملكية
                }
                else
                {
                    // حساب الإجمالي بطريقة بديلة إذا لم تكن العناصر المتوقعة موجودة
                    foreach (var item in _liabilitiesEquityItems)
                    {
                        if (item.IsBold && !item.IsHeader)
                        {
                            totalLiabilitiesEquity += item.Amount;
                        }
                    }
                }

                txtTotalAssets.Text = totalAssets.ToString("N2");
                txtTotalLiabilitiesEquity.Text = totalLiabilitiesEquity.ToString("N2");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث الميزانية العمومية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnShow_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // في التطبيق الحقيقي، هنا سيتم إعادة تحميل البيانات من قاعدة البيانات حسب الفترة المحددة
                MessageBox.Show("تم تحديث البيانات حسب الفترة المحددة", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري طباعة التقرير...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnExport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري تصدير التقرير إلى Excel...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnBackToAccounts_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // العودة إلى صفحة الحسابات الرئيسية
                NavigationService?.Navigate(new AccountsPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء العودة للصفحة الرئيسية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCloseFiscalYear_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("جاري إقفال السنة المالية...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class TrialBalanceItem
    {
        public string AccountNumber { get; set; }
        public string AccountName { get; set; }
        public double DebitBalance { get; set; }
        public double CreditBalance { get; set; }
    }

    public class FinancialStatementItem
    {
        public string Description { get; set; }
        public double Amount { get; set; }
        public bool IsBold { get; set; }
        public bool IsHeader { get; set; }
    }
}
